<script lang="ts" setup>
import {onMounted, onUnmounted, ref, watch} from 'vue'
import {tunnelStore, useMqttStore,useTokenStore} from './store/index.ts'
import {invoke} from '@tauri-apps/api/tauri';
import {listen} from '@tauri-apps/api/event'
import type WiresockStateModel from './models/WiresockStateModel.ts'
import {useRouter} from "vue-router";
import {getAccountInfoAPI} from "./api/network.ts";

const router = useRouter()
const mqttStore = useMqttStore()
const tokenStore = useTokenStore()
const tunnelStoreData: any = tunnelStore()
const TapUsr_VERSION = '9.24.7'
const aliveTimer:any = ref(null)
onMounted(() => {
  console.log('App mounted')
  setupTauriEventListeners()
  getWiresockVersion()
  dealSetting()
  getAccountInfo()

})
onUnmounted(() => {
  clearInterval(aliveTimer.value)
  aliveTimer.value = null
})
const getToken = () => {
  return sessionStorage.getItem('token') || ''
}
/**
 * 监听 tunnelManager 对象深度变化，处理系统托盘更新和持久化存储
 */
const getAccountInfo = async () => {
  let token = getToken()
  if (token) {
    let res: any = await getAccountInfoAPI()
    if (res.msg === 'success') {
      mqttStore.setClientName(res.result.clientName)
      // 连接MQTT
      mqttStore.connect()
    }
    tokenStore.loginToken(token)
  } else {
    router.push('/login')
  }
}
/**
 * 监听 tunnelManager 对象深度变化，处理系统托盘更新和持久化存储
 * @param {function} watchSource - 监听源，返回 tunnelStoreData.tunnelManager 的 getter 函数
 * @param {function} callback - 变化回调函数，接收 (newVal, oldVal) 参数
 * @param {Object} options - 监听选项，配置 {deep: true} 启用深度监听
 */
watch(() => tunnelStoreData.tunnelManager, (newVal) => {
  console.log('change to tunnelManager')
  tunnelStoreData.setTunnelManagerRef(tunnelStoreData.tunnelManager)
  // 系统托盘菜单更新逻辑：当启用最小化到托盘时，生成连接菜单项并更新系统托盘
  if (tunnelStoreData.settings.minimizeToTray) {
    // 将隧道数据映射为菜单项格式 [tunnelId, tunnelName]
    const connectMenuItems = Object.entries(newVal.tunnels).map(([tunnelId, tunnel]) => [
      tunnelId,
      (tunnel as { name: string }).name
    ])
    // 调用底层接口更新系统托盘连接菜单
    void invoke('update_systray_connect_menu_items', {items: connectMenuItems})
  }

  // 持久化存储策略：根据首次变更标志决定存储行为
  if (tunnelStoreData.isTunnelManagerFirstChange) {
    // 首次变更时仅更新标志状态，避免立即存储
    tunnelStoreData.setIsSelectedTunnelIDFirstChange(false)
  } else {
    // 非首次变更时直接持久化存储隧道数据
    tunnelStoreData.saveTunnelsToStorage(newVal.tunnels)
  }
}, {deep: true})
// 监听selectedTunnelID
watch(() => tunnelStoreData.selectedTunnelID, (newVal) => {
  if (tunnelStoreData.isSelectedTunnelIDFirstChange) {
    tunnelStoreData.setIsSelectedTunnelIDFirstChange(false)
  } else {
    if (newVal !== null) {
      tunnelStoreData.saveSelectedTunnelIDToStorage(newVal)
    } else {
      tunnelStoreData.deleteSelectedTunnelIDKeyFromStorage()
    }
  }
})
/**
 * 监听 wiresock 状态变化并执行关联操作
 *
 * @监听源 {function} () => tunnelStoreData.wiresockState - 监听的响应式状态对象
 * @回调处理 {function} (newVal) => {...} - 状态变化时触发的操作
 * @配置项 {Object} {deep: true} - 深度监听配置
 */
watch(() => tunnelStoreData.wiresockState, (newVal) => {
  // 保存隧道状态用于后续状态变更比较
  const prevTunnelStatus = tunnelStoreData.prevTunnelStatusRef
  const currentTunnelStatus = newVal?.tunnel_status
  tunnelStoreData.setPrevTunnelStatusRef(currentTunnelStatus)

  /* 自动连接策略：
    1. 首次检测到 wiresock 停止状态时触发
    2. 根据配置自动建立指定隧道连接 */
  if (!tunnelStoreData.hasRunAutoConnect && newVal?.wiresock_status === 'STOPPED') {
    tunnelStoreData.setHasRunAutoConnect(true)
    if (tunnelStoreData.settings.autoConnectTunnelID) {
      enableTunnel(tunnelStoreData.settings.autoConnectTunnelID)
    }
  }
  /* 系统托盘状态同步策略：
    [连接成功] 更新图标、工具提示、添加断开菜单项
    [连接断开] 恢复图标、更新提示、移除断开菜单项 */
  if (newVal && prevTunnelStatus === 'DISCONNECTED' && currentTunnelStatus === 'CONNECTED') {
    void invoke('change_icon', {enabled: true})
    const connectedTunnel = tunnelStoreData.tunnelManager.getTunnel(newVal.tunnel_id)
    void invoke('change_systray_tooltip', {tooltip: `Connected: ${connectedTunnel?.name}`})
    void invoke('add_or_update_systray_menu_item', {itemId: 'disconnect', itemLabel: 'Disconnect'})
  } else if (newVal && prevTunnelStatus === 'CONNECTED' && currentTunnelStatus === 'DISCONNECTED') {
    void invoke('change_icon', {enabled: false})
    void invoke('change_systray_tooltip', {tooltip: 'TunnlTo: Disconnected'})
    void invoke('remove_systray_menu_item', {itemId: 'disconnect'})
  }
}, {deep: true})

/**
 * 处理应用设置变更的核心函数，主要功能：
 * - 控制应用启动时的窗口显示状态
 * - 同步前端设置到Rust后端
 * - 持久化存储设置变更
 * 注意：该函数无参数传递和返回值
 */
const dealSetting = () => {
  /* 窗口显示控制：若未设置启动最小化，则显示主窗口 */
  if (!tunnelStoreData.settings.startMinimized) {
    void invoke('show_app')
  }
  /* 向后端同步三项关键设置 */
  // 最小化到托盘设置
  void invoke('set_minimize_to_tray', {value: tunnelStoreData.settings.minimizeToTray})
  // 日志保留数量设置
  void invoke('set_log_limit', {value: tunnelStoreData.settings.logLimit})
  /* 设置持久化策略： 首次变更跳过存储（通常为初始化值），后续变更写入本地存储 */
  if (tunnelStoreData.isSettingsFirstChange.current) {
    tunnelStoreData.isSettingsFirstChange.current = false
  } else {
    tunnelStoreData.saveSettingsToStorage(tunnelStoreData.settings)
  }
}

/**
 * 设置 Tauri 事件监听器
 *
 * 功能说明：
 * 1. 监听 wiresock_state 事件，更新隧道状态存储
 * 2. 监听系统托盘连接菜单点击事件，触发隧道连接
 * 3. 主动获取当前 wiresock 状态
 *
 * 返回值：Promise<void> 异步操作的状态标识
 */
async function setupTauriEventListeners(): Promise<void> {
  console.log('Setting up wiresock_state listener')
  // 监听 wiresock 状态变更事件
  // - 接收事件载荷转换为 WiresockStateModel
  // - 更新隧道存储状态并打印日志
  await listen('wiresock_state', function (event) {
    tunnelStoreData.setWiresockState(event.payload as WiresockStateModel)
    console.log('wiresock_state  调用', tunnelStoreData.wiresockState)
  })
  // 初始化时主动获取当前 wiresock 状态
  console.log('Retrieving wiresock_state from Tauri')
  await invoke('get_wiresock_state')
}

/**
 * 获取 WireSock 版本信息并更新安装状态
 *
 * 异步检测 WireSock 组件的安装状态和版本兼容性，通过调用系统命令获取版本信息。
 * 根据检测结果更新隧道存储数据中的 WireSock 安装详细信息，包含三个状态：
 * - 未安装状态
 * - 已安装但版本不匹配
 * - 已安装且版本匹配
 *
 * @returns {Promise<void>} 无返回值
 */
async function getWiresockVersion(): Promise<void> {
  getTapUsrVersion()
}

/**
 * 获取 tap-usr-windows 版本信息并更新安装状态
 *
 * 异步检测 tap-usr-windows 组件的安装状态和版本兼容性，通过调用系统命令获取版本信息。
 * 根据检测结果更新隧道存储数据中的 tap-usr-windows 安装详细信息，包含三个状态：
 * - 未安装状态
 * - 已安装但版本不匹配
 * - 已安装且版本匹配
 *
 * @returns {Promise<void>} 无返回值
 */
async function getTapUsrVersion(): Promise<void> {
  console.log('Checking tap-usr-windows version')
  // 调用系统命令获取 WireSock 版本信息
  const result: string = await invoke('get_tap_usr_version')
  /* 处理未安装情况 */
  if (result === 'TAP-USR-Windows_not_installed') {
    console.log('tap-usr-windows is not installed')
    router.push('/')
    return
  }
  /* 处理已安装情况下的版本校验 */
  console.log('tap-usr-windows installed version:', result)
  if (result === TapUsr_VERSION) {
    console.log('Supported version of tap-usr-windows is installed')
    const adapters: any[] = await invoke('get_network_adapters_all');
    const targetAdapter = adapters.find(adapter => adapter.description.includes('TAP-Windows'));
    if (!targetAdapter) {
      console.log('安装网卡页面');
      router.push('/')
    }
  } else {
    console.log('Unsupported tap-usr-windows version installed')
    router.push('/')
  }
}

/**
 * 启用指定隧道（通过 WireSock 实现）
 *
 * @param tunnelId - 需要启用的隧道唯一标识符
 * @returns 无返回值
 */
function enableTunnel(tunnelId: string): void {
  console.log('enableTunnel called with tunnelId:', tunnelId)
  // 从隧道存储中获取最新隧道数据（避免直接使用可能过时的 tunnelManager 监听器数据）
  // 重要：使用 tunnelStoreData 确保获取的是实时数据，直接使用 tunnelManager 可能获取到初始化时的陈旧数据
  const tunnelData = tunnelStoreData.tunnelManagerRef.current.getTunnel(tunnelId)
  if (tunnelData != null) {
    if (tunnelData != null) {
      // 执行隧道启用核心逻辑：组合隧道配置和日志设置，调用底层 WireSock 接口
      console.log('Enabling tunnel:', {
        tunnel: tunnelData,
        logLevel: tunnelStoreData.settings.logLevel,
      })
      // 异步调用系统命令启用隧道，并捕获可能的异常
      invoke('enable_wiresock', {
        tunnel: tunnelData,
        logLevel: tunnelStoreData.settings.logLevel,
      }).catch((error) => {
        // 错误处理：WireSock 进程启动失败或隧道连接异常
        console.error('Invoking enable_wiresock returned error: ', error)
      })
    } else {
      // 无效隧道 ID 处理：未在存储中找到对应隧道数据
      console.error('Tunnel not found for ID:', tunnelId)
    }
  }
}
</script>

<template>
  <router-view></router-view>
</template>

<style scoped>
.logo.vite:hover {
  filter: drop-shadow(0 0 2em #747bff);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #249b73);
}

</style>
<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
  background-image: linear-gradient(163deg, #E6EDFF 9%, #D2DFFF 91%);
  border-radius: 0 0 12px 12px;
}

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: #0f0f0f;
  background-color: #f6f6f6;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
@font-face {
  font-family: 'PingFangHK-Medium';
  src: url('./assets/font/PingFangHK-Regular.otf');
  font-weight: 400; /* 常规粗细 */
  font-style: normal;
  font-display: swap; /* 优化字体加载行为 */
}
*{
  font-family: 'PingFangHK-Medium'!important;
}
</style>
