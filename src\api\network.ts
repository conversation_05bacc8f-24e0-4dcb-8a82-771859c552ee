import request from '../plugins/request'
import {requestIp} from './config.ts'
export const getAccountInfoAPI = () => request.get(`${requestIp}/client/account`, {})

export const modifyNameAPI = (data: any) => request.put(`${requestIp}/client/accountName`, data)

export const modifyPasswordAPI = (data: any) => request.put(`${requestIp}/client/accountPassword`, data)

export const getThreeNetworkSpeedAPI = () => request.get(`${requestIp}/client/netSpeed`, {})

export const getNetworkAndPeerAPI = () => request.get(`${requestIp}/client/peerInfo`, {})

export const getNetworkAndClientAPI = () => request.get(`${requestIp}/client/peers`, {})
export const changeNetworkStatusAPI = (data:any) => request.put(`${requestIp}/client/peerEnable`, data)

export const getAllNetworkAPI = (data:any) => request.get(`${requestIp}/client/peers2`, {
    params: data
})

export const getNetworkClientAPI = (data:any) => request.get(`${requestIp}/client/endpoint/${data.sn}`, {
    params: data
})
