import { defineStore } from 'pinia'
import { reactive, toRefs } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const initData: any = reactive({
    routes: [], // 保存整体路由，用于菜单整体展示
    addRoutes: [] // 需要添加的菜单权限列表, 留给退出登录时候清除路由使用
  })
  // 设置store中记录的整体路由
  function addAllRoutes(routes: any) {
    initData.routes = routes
  }

  return { ...toRefs(initData), addAllRoutes }
},{persist: true})
