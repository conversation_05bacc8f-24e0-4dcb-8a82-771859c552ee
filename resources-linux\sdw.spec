Name:     sdw
Version:  %{version}
Release:  1%{?dist}
Summary:  sdw desktop client

License:  Apache-2.0
URL:      https://www.usr.cn/
Requires: libappindicator-gtk3 webkit2gtk4.0

%description
sdw client for managing WireGuard VPN connections

%install
%{__mkdir} -p %{buildroot}/%{_bindir}
%{__mkdir} -p %{buildroot}/%{_sbindir}
%{__mkdir} -p %{buildroot}/%{_prefix}/lib/systemd/system
%{__mkdir} -p %{buildroot}/%{_prefix}/lib/sdw/icons
%{__mkdir} -p %{buildroot}/%{_datadir}/applications
%{__mkdir} -p %{buildroot}/%{_datadir}/icons/hicolor/128x128/apps
%{__mkdir} -p %{buildroot}/%{_datadir}/icons/hicolor/256x256@2/apps
%{__mkdir} -p %{buildroot}/%{_datadir}/icons/hicolor/32x32/apps
%{__install} -m 755 src-tauri/target/release/sdw %{buildroot}/%{_bindir}/
%{__install} -m 755 src-tauri/target/release/sdw-service %{buildroot}/%{_sbindir}/
%{__install} -m 644 resources-linux/sdw-service.service %{buildroot}/%{_prefix}/lib/systemd/system/
%{__install} -m 644 resources-linux/sdw.desktop %{buildroot}/%{_datadir}/applications/sdw.desktop
%{__install} -m 644 src-tauri/icons/icon-color-32x32.png %{buildroot}/%{_prefix}/lib/sdw/icons/icon-color-32x32.png
%{__install} -m 644 src-tauri/icons/128x128.png %{buildroot}/%{_datadir}/icons/hicolor/128x128/apps/sdw.png
%{__install} -m 644 src-tauri/icons/<EMAIL> %{buildroot}/%{_datadir}/icons/hicolor/256x256@2/apps/sdw.png
%{__install} -m 644 src-tauri/icons/32x32.png %{buildroot}/%{_datadir}/icons/hicolor/32x32/apps/sdw.png

%post
# %{systemd_post} sdw-service.service
if [ $1 -eq 1 ]; then
    systemctl daemon-reload
    systemctl enable sdw-service.service
    systemctl start sdw-service.service
fi

%preun
# %{systemd_preun} sdw-service.service
if [ $1 -eq 0 ]; then
    systemctl stop sdw-service.service
    systemctl disable sdw-service.service
fi

%postun
# %{systemd_postun} sdw-service.service
systemctl daemon-reload

%files
%{_bindir}/sdw
%{_sbindir}/sdw-service
%{_prefix}/lib/sdw/icons/icon-color-32x32.png
%{_prefix}/lib/systemd/system/sdw-service.service
%{_datadir}/applications/sdw.desktop
%{_datadir}/icons/hicolor/128x128/apps/sdw.png
%{_datadir}/icons/hicolor/256x256@2/apps/sdw.png
%{_datadir}/icons/hicolor/32x32/apps/sdw.png
