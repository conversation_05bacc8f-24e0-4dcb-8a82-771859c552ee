<script lang="ts" setup>
import {onMounted, onScopeDispose, onUnmounted, ref, watch} from "vue";
import {
  getNetworkAndPeerAPI,
  getThreeNetworkSpeedAPI
} from "../../api/network.ts";
import {tunnelStore, useMqttStore, useN3nStore} from "../../store";
import {invoke} from '@tauri-apps/api/tauri';
import { ElMessage} from "element-plus";

// pinia
const mqttStore = useMqttStore()
const n3nStore: any = useN3nStore()
const tunnelStoreData: any = tunnelStore()
// 二层组网的网卡
const selectedAdapter: any = ref('')
// 图表数据
const trafficTimer: any = ref(null)
const trafficData: any = ref([])
const currentTrafficData: any = ref({
  up: '0bps',
  down: '0bps',
})
onMounted(async () => {
  // 维持数据统一
  trafficData.value = mqttStore.chartData
  console.log(n3nStore, tunnelStoreData, mqttStore, '进入组网函数页面')
})

function dataToTunnel(info: any, wan: any):any {
  const {allowIps, wgAddress, wgPrivateKey} = wan.peer
  const IPList = wan.endPoint.split(':')
  const ipv4 = wgAddress
  const editedTunnel: any = {
    id: info.id,
    name: info.pId,
    interface: {
      ipv4Address: ipv4,
      ipv6Address: '',
      port: '',
      privateKey: wgPrivateKey,
      dns: '',
      mtu: wan.wgMtu.toString(),
    },
    peer: {
      endpoint: IPList[0],
      port: IPList[1] + '',
      publicKey: wan.wgPublicKey,
      presharedKey: '',
      persistentKeepalive: '5',
    },
    rules: {
      allowed: {
        apps: '',
        folders: '',
        ipAddresses: allowIps.join(','),
      },
      disallowed: {
        apps: '',
        folders: '',
        ipAddresses: '',
      },
    },
  }
  return editedTunnel
}


/**
 * 开启三层组网
 */
const enableThree = async (tunnelData: any) => {
  if (!tunnelData) {
    throw new Error('Tunnel not found')
  }
  mqttStore.setChartData([])
  console.log(tunnelData, 'tunnelData')
  invoke('enable_wiresock', {
    tunnel: tunnelData,
    logLevel: tunnelStoreData.settings.logLevel,
  }).catch((error) => {
    console.error('启用失败:', error)
    throw error // 保持错误冒泡
  })
}
/**
 * 开启二层组网
 */
const enableTwo = async (tunnel: any) => {
  // 启动n3n节点，无法直接设置网关
  const netmask = mqttStore.wgConfig?.secondNetmask,
  gretapmtu = mqttStore.wgConfig?.gretapMtu.toString() ,
  remoteip = mqttStore.wgConfig?.wgAddress,
  ip = mqttStore.wgConfig?.secondIp,
  localip = tunnel.interface.ipv4Address.replace('/32', '')

  // const netmask = "*************",
  // gretapmtu = "1392" ,
  // remoteip = mqttStore.wgConfig?.wgAddress,
  // ip = "*************",
  //   // tunnel.interface.ipv4Address 去除后面的/32
  // localip = tunnel.interface.ipv4Address.replace('/32', '')

  console.log('开启vpn之前清除网关', selectedAdapter.value.name || n3nStore.network?.name)
  // 尝试清除二层vpn，避免重复开启
  await invoke('reset_network', {
    adapter: selectedAdapter.value.name || n3nStore.network?.name || 'usr',
  })
  // await invoke('stop_monitor')
  mqttStore.setChartData([])
  console.log("ip",ip,",netmask:",netmask,",localip:",localip,",remoteip:",remoteip)
  // 开启n3n
  const result: any = await invoke('start_vpn', {
    tunnel,
    ip,
    netmask,
    gretapmtu,
    localip,
    remoteip
  });
  if (result == "VPN 已经运行") {
    ElMessage.error('程序启动失败，请重启应用')
    return
  }
  // 获取n3n设置的网卡信息
  const res: any = await getAdapterWithRetry(ip)
  if (res.code === 1) {
    n3nStore.setNetwork(res.data)
    selectedAdapter.value = res.data
    console.log(res.data,mqttStore.wgConfig, 'n3n获取网卡信息')
    
    // 设置网卡网关掩码
    // const setGatewayResult = await invoke('set_static_ip', {
    //   adapter: selectedAdapter.value.name,
    //   ip: ip,
    //   mask: mask,
    //   gateway: gateway
    // })
    // console.log(setGatewayResult, '设置网卡网关掩码')
    // 组网流程完成
    console.log(selectedAdapter.value, '启动当前网卡监控')
    // await invoke('start_monitor', {interface: selectedAdapter.value.name})
    n3nStore.setStatus(true)
  } else {
    n3nStore.setStatus(false)
    await invoke('stop_vpn')
    ElMessage.error('二层组网开启失败！')
  }
}

/**
 * 启用vpn
 */
function enableTunnel(tunnelData: any) {
  if (mqttStore.wgConfig?.level == 3) {
    enableThree(tunnelData)
  } else {
    enableTwo(tunnelData)
  }
}

/**
 * 10秒内获取网卡有无被设置成二层的Ip，有就返回code值1，没有就code值0
 */
async function getAdapterWithRetry(ip: string): Promise<{ code: number; data: any }> {
  let attempts = 0;
  const maxAttempts = 5;
  const delay = 2000;

  while (attempts < maxAttempts) {
    const adapters: any = await invoke('get_network_adapters_all');
    console.log(adapters, 'adapters')
    if (adapters.length > 0) {
      for (let i in adapters) {
        if (adapters[i].ipv4 === ip) {
          return {
            code: 1,
            data: adapters[i]
          }
        }
      }
    }
    attempts++;
    if (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, delay)); // 等待重试
    }
  }
  return {
    code: 0,
    data: '五次尝试均未获取到网关适配器'
  }; // 最终失败返回错误信息
}

/**
 * 关闭组网（二层/三层）
 */
async function disableTunnel(): Promise<void> {
  try {
    console.log("tools : disable_wiresock")
    await invoke('disable_wiresock')
    await invoke('stop_vpn')
    // await invoke('stop_monitor')
    console.log('清除网关', selectedAdapter.value.name || n3nStore.network?.name)
    // 清除网关
    let result = await invoke('reset_network', {
      adapter: selectedAdapter.value.name || n3nStore.network?.name,
    })
    console.log(result, '清除网关结果')
  } catch (error) {
    console.error('关闭组网失败:', error)
    throw error // 保持错误冒泡
  }
}

/**
 * 获取三层组网流量表格数据
 */
const getNetworkSpeed = async () => {
  let res: any = await getThreeNetworkSpeedAPI()
  if (res.msg == 'success') {
    // 判断 trafficData.value.length >= 60，就删除第一个，新增一条数据，维持数据始终是60条
    if (trafficData.value.length >= 60) {
      trafficData.value.shift()
    }
    const {up, down, timestamp} = res.result
    trafficData.value.push({
      up:up,
      down:down,
      timestamp,
      upStr: convertBytes(up),
      downStr: convertBytes(down),
      timeStr: timestampToTime(timestamp),
    })
    currentTrafficData.value = {
      up: convertBytes(up),
      down: convertBytes(down),
      time: timestampToTime(timestamp),
    }
    mqttStore.setCurrentTrafficData(currentTrafficData.value)
    mqttStore.setChartData(trafficData.value)
  }
}

/**
 * 格式化三层组网流速
 */
function convertBytes(bytes: any) {
  // 将字节转换为比特
  const bitsPerSecond = bytes;
  // 定义单位数组
  const units = ['bps', 'kbps', 'Mbps'];
  // 初始化速度和单位索引
  let speed = bitsPerSecond;
  let unitIndex = 0;
  // 循环判断，直到找到合适的单位
  while (speed >= 1024 && unitIndex < units.length - 1) {
    speed /= 1024;
    unitIndex++;
  }
  // 格式化输出，保留2位小数
  return `${speed.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化十位时间戳为分秒
 */
function timestampToTime(timestamp: any) {
  const date = new Date(timestamp * 1000); // 10位时间戳需乘以1000
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}
const startThreeMonitor = async (newTunnelStatus:any) => {
  if (newTunnelStatus === 'CONNECTED') {
    if (trafficTimer.value==null) {
      getNetworkSpeed();
      // 设置新定时器，避免重复创建
      let num = 0
      clearInterval(trafficTimer.value)
      trafficTimer.value = null
      trafficTimer.value = setInterval(async () => {
        getNetworkSpeed();
        num = num+10
        if(num%60===0&&mqttStore.wgConfig.wgAddress){
          await invoke('three_active_ping', {ip:mqttStore.wgConfig?.wgAddress})
        }
      }, 10000);
    }
  }else{
    clearInterval(trafficTimer.value)
    trafficTimer.value = null
  }
}
/**
 * 监听二层组网三层组网的开启状态，生成表格，上报数据
 */
watch(
  () => [
    tunnelStoreData.wiresockState?.tunnel_status ?? null,
    mqttStore.wgConfig?.level ?? null
  ],
  async (
    [newTunnelStatus, newLevel],
    oldVal // 移除默认值赋值
  ) => {
    const [oldTunnelStatus = null, oldLevel = null] = oldVal || []; // 处理undefined情况
    // 只有当两个参数都发生变化时才执行逻辑
    console.log(newTunnelStatus , oldTunnelStatus , newLevel , oldLevel,'监控组网状态')
    if (newTunnelStatus === oldTunnelStatus && newLevel === oldLevel) {
      startThreeMonitor(newTunnelStatus)
    }else{
      if (newTunnelStatus === 'CONNECTED') {
        mqttStore.startReportThree()
        startThreeMonitor(newTunnelStatus)
      }
    }
  },
  {immediate: true}
)
/**
 * 监听 mqtt的config变化，如果变化了需要关闭组网并重新开启
 * 返回值有三种
 * level ：2 二层组网
 * level ：3 三层组网
 * level:随机数 关闭组网
 */
watch(
  () => mqttStore.wgConfig,
  async () => {
    // 关闭二层三层所有的连接
    if (mqttStore.wgConfig?.level !== 2 && mqttStore.wgConfig?.level !== 3) {
      console.log('监听config 变化关闭组网：',mqttStore.wgConfig)
      n3nStore.setStatus(false)
      resetTimer()
      await disableTunnel()
      return
    } else {
      if (mqttStore.wgConfig?.level === 2) {
        const osType = await invoke<string>('get_os_type');
        console.log('当前操作系统:', osType);  // 输出 "windows" 或 "macos" 等
        if (osType !== 'windows') {
          console.log('不是Windows系统，不支持二层，关闭组网')
          ElMessage.error('当前系统暂不支持二层组网！')
          n3nStore.setStatus(false)
          resetTimer()
          await disableTunnel()
            return
        }
      }
    }
    let resPeer: any = await getNetworkAndPeerAPI()
    if (resPeer.msg === 'success') {
      console.log( mqttStore.wgConfig,'收到组网数据')
      //接收的数据转换为tunnel
      let tunnel = dataToTunnel(resPeer.result, mqttStore.wgConfig)
      //监听到配置文件修改,等待设置配置文件
      setTimeout(() => {
          enableTunnel(tunnel)
      }, 1000)
    }
  },
  {immediate: true}
)
const resetTimer = () => {
  if(trafficTimer.value!=null){
    clearInterval(trafficTimer.value)
    trafficTimer.value = null
  }
}
// 销毁定时器
onUnmounted(() => {
  resetTimer()
})
onScopeDispose(() => {
  resetTimer()
})
</script>

<template>
<div style="display: none"></div>
</template>




