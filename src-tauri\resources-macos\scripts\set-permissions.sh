#!/bin/bash

# 设置macOS资源文件权限脚本
# 在打包前运行此脚本确保所有文件具有正确的权限

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RESOURCES_MACOS_DIR="$(dirname "$SCRIPT_DIR")"

echo "设置macOS资源文件权限..."
echo "资源目录: $RESOURCES_MACOS_DIR"

# 设置脚本文件权限
echo "设置脚本文件权限..."
chmod +x "$RESOURCES_MACOS_DIR/scripts/postinstall"
chmod +x "$RESOURCES_MACOS_DIR/scripts/check-permissions.sh"
chmod +x "$RESOURCES_MACOS_DIR/scripts/set-permissions.sh"
chmod +x "$RESOURCES_MACOS_DIR/resources/uninstall.sh"

# 设置二进制文件权限
echo "设置二进制文件权限..."
if [ -d "$RESOURCES_MACOS_DIR/binaries" ]; then
    chmod +x "$RESOURCES_MACOS_DIR/binaries/"*
fi

# 设置资源文件权限
echo "设置资源文件权限..."
chmod 644 "$RESOURCES_MACOS_DIR/resources/usr.sdw.plist"
chmod 644 "$RESOURCES_MACOS_DIR/README.md"

# 验证权限设置
echo "验证权限设置..."
echo "脚本文件:"
ls -la "$RESOURCES_MACOS_DIR/scripts/"

echo "资源文件:"
ls -la "$RESOURCES_MACOS_DIR/resources/"

if [ -d "$RESOURCES_MACOS_DIR/binaries" ]; then
    echo "二进制文件:"
    ls -la "$RESOURCES_MACOS_DIR/binaries/"
fi

echo "权限设置完成!"
