<script setup lang="ts">
import {ElMessage} from "element-plus";
const props = defineProps({
  ip: {
    type: String,
    default: ''
  }
})
const formatIP = (ip: any) => {
  const ipv4 = ip.split('/')
  if (ipv4.length == 2) {
    return ipv4[0]
  } else {
    return ip
  }
}
const copyIP = () => {
  const   ip:any = formatIP(props.ip)
  navigator.clipboard.writeText(ip).then(() => {
    ElMessage.success('复制成功')
    console.log('IP 地址已复制到剪贴板', ip)
  }).catch(err => {
    ElMessage.error('复制失败')
    console.error('无法复制 IP 地址: ', err);
  });
}
</script>

<template>
  <svg fill="none" height="10" style="margin-left: 5px" viewBox="0 0 10 10"
       width="10" xmlns="http://www.w3.org/2000/svg" @click="copyIP">
    <rect height="6" rx="0.5" stroke="#7588BB" stroke-width="0.7" width="5.01506" x="3" y="2.75"/>
    <path
      d="M6.5 1.75V1.75C6.5 1.47386 6.27614 1.25 6 1.25H2C1.72386 1.25 1.5 1.47386 1.5 1.75V6.75C1.5 7.02614 1.72386 7.25 2 7.25H2.125"
      stroke="#7588BB" stroke-width="0.7"/>
  </svg>
</template>

<style scoped>

</style>
