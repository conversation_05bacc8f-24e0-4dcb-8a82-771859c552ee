<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="180px" viewBox="0 0 180 180" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>4数据展示/缺省/无网络</title>
    <defs>
        <linearGradient x1="81.3167033%" y1="43.868319%" x2="27.6058235%" y2="59.9154713%" id="linearGradient-1">
            <stop stop-color="#EEF3FD" offset="0%"></stop>
            <stop stop-color="#F4F7FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-7.18380649%" y1="57.9595779%" x2="87.4167012%" y2="50%" id="linearGradient-2">
            <stop stop-color="#FAFAFB" offset="0%"></stop>
            <stop stop-color="#F2F6FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="39.4760537%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-3">
            <stop stop-color="#E8EEFD" offset="0%"></stop>
            <stop stop-color="#C6CFE6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="45.3226905%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-4">
            <stop stop-color="#E8EFFE" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.5427556%" y1="12.9689754%" x2="50%" y2="91.6982073%" id="linearGradient-5">
            <stop stop-color="#EAF1FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="35.6757397%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-6">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="24.744643%" x2="50%" y2="77.5273322%" id="linearGradient-7">
            <stop stop-color="#EDF1FC" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.9430786%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-8">
            <stop stop-color="#E8EFFE" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="45.4314724%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-9">
            <stop stop-color="#E8EFFE" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.8568338%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-10">
            <stop stop-color="#E8EFFE" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="46.9127413%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-11">
            <stop stop-color="#E8EFFE" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="32.3913017%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-12">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="32.6529658%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-13">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="13.6421406%" x2="50%" y2="89.6286172%" id="linearGradient-14">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="13.6421406%" x2="50%" y2="89.6286172%" id="linearGradient-15">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="67.9189766%" y1="9.55065553%" x2="16.0391079%" y2="76.6893209%" id="linearGradient-16">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-17">
            <stop stop-color="#EAF1FF" offset="11.2333711%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="60.7368096%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <path d="M80,12.1639871 C123.637896,12.1639871 159.115295,22.864128 159.983709,36.1645593 L160,36.1639871 L160,70.1639871 L0,70.1639871 L0,36.1639871 L0.0162909092,36.1645593 C0.8847045,22.864128 36.3621044,12.1639871 80,12.1639871 Z" id="path-18"></path>
        <linearGradient x1="50%" y1="12.686452%" x2="50%" y2="159.141656%" id="linearGradient-20">
            <stop stop-color="#D0D9F0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="63.6271443%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-6.41910769%" x2="50%" y2="100%" id="linearGradient-21">
            <stop stop-color="#D0D9F0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="66.0818674%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-6.41910769%" x2="50%" y2="100%" id="linearGradient-22">
            <stop stop-color="#D0D9F0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="61.8443202%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="38.0260877%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-23">
            <stop stop-color="#DDE6FC" offset="0%"></stop>
            <stop stop-color="#9FA5B6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="30.4813085%" x2="50%" y2="71.2745956%" id="linearGradient-24">
            <stop stop-color="#E8EEFD" offset="0%"></stop>
            <stop stop-color="#C6CFE6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="29.0082352%" x2="50%" y2="72.8801867%" id="linearGradient-25">
            <stop stop-color="#DDE6FC" offset="0%"></stop>
            <stop stop-color="#9FA5B6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="26.2941322%" x2="50%" y2="75.8384507%" id="linearGradient-26">
            <stop stop-color="#E8EEFD" offset="0%"></stop>
            <stop stop-color="#C6CFE6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="PC客户端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="已入网-缺省" transform="translate(-389, -290)">
            <g id="编组-2" transform="translate(74, 120)">
                <g id="1通用/2Icon图标/缺省/无网络" transform="translate(315, 170)">
                    <rect id="矩形" x="0" y="0" width="180" height="180"></rect>
                    <g id="Background" transform="translate(10, 21)">
                        <path d="M123.417914,31.41863 C94.8358289,53.5152255 152,52.5545498 152,58.3670177 C152,68.1223392 138.786773,73 112.360318,73 C44.3346179,71.2716881 16.0544837,60.308521 27.5199157,40.1104987 C44.7180636,9.8134653 -8.49983288,33.1460504 1.18906725,9.80227448 C10.8779674,-13.5415014 152,9.32203451 123.417914,31.41863 Z" fill="url(#linearGradient-1)" transform="translate(76, 36.5) scale(1, -1) translate(-76, -36.5)"></path>
                        <path d="M144.5,41.9206736 C153.612698,41.9206736 161,42.9454983 161,37.6280649 C161,32.3106315 153.612698,28 144.5,28 C135.387302,28 128,32.3106315 128,37.6280649 C128,42.9454983 135.387302,41.9206736 144.5,41.9206736 Z" fill="url(#linearGradient-2)"></path>
                    </g>
                    <g id="编组-11" transform="translate(29, 79)">
                        <path d="M13.6,0 L13.5997806,0.705348704 C13.8625596,0.340034128 14.0841104,0.0987910349 14.1547276,0.024411507 L14.1782565,0 C14.1782565,0 14.1834089,0.0093783296 14.1927755,0.0275301223 L14.2325802,0.107701023 C14.3711495,0.396179763 14.7310991,1.23662534 14.7913771,2.29306207 C15.4386708,1.68081317 15.9756554,1.33333333 15.9756554,1.33333333 C15.9756554,1.33333333 15.994732,1.48064746 15.9991114,1.73403378 L15.9991125,1.94331765 C15.9876824,2.62696846 15.8591027,3.79541298 15.2286685,4.97887995 C15.0063415,5.39623786 14.7406877,5.77963411 14.4627791,6.1230167 C14.763862,7.02538486 14.7968422,7.73883478 14.799749,7.94179262 L14.7997807,8 C14.7997807,8 14.2162842,7.70631284 13.4692968,7.16966039 C12.8859481,7.70126599 12.424328,8 12.424328,8 C12.424328,8 12.4052578,7.85267874 12.4008857,7.59928223 L12.4008902,7.3899902 C12.4042819,7.18741936 12.4179581,6.94228684 12.4519242,6.66681369 L11.2,6.66666667 L11.199,4.94 L11.1453231,4.86294117 C10.0829314,3.27554243 10.004798,1.70900254 10.000251,1.39153811 L10.0002193,1.33333333 C10.0002193,1.33333333 10.5166501,1.59324352 11.1997955,2.07086145 L11.2,0 L13.6,0 Z" id="形状结合" fill="url(#linearGradient-3)"></path>
                        <path d="M13.5,22 L13.5,22 C11.0250468,22 9,20.2702533 9,18.1560591 L9,7.84400749 C9,5.72981337 11.0250468,4 13.5,4 C15.9750312,4 18,5.72981337 18,7.84400749 L18,18.1560591 C18,20.2702533 15.9750312,22 13.5,22" id="Fill-10" fill="url(#linearGradient-4)"></path>
                        <path d="M13.5,53 L13.5,53 C11.0250468,53 9,51.3039894 9,49.2311747 L9,24.7688253 C9,22.6959453 11.0250468,21 13.5,21 C15.9750312,21 18,22.6959453 18,24.7688253 L18,49.2311747 C18,51.3039894 15.9750312,53 13.5,53" id="Fill-12" fill="url(#linearGradient-5)"></path>
                        <path d="M14,31.5008287 L14,32.6770147 C14,33.9599964 13.1777805,35 12.1635907,35 L5.96852841,35 C2.67222762,35 0,31.6197375 0,27.4499754 L0,22.1318371 C0,20.4021771 1.10847499,19 2.47583779,19 C3.84320058,19 4.95167557,20.4021771 4.95167557,22.1318371 L4.95167557,26.6857053 C4.95167557,28.0620795 5.83372997,29.1778435 6.9218067,29.1778435 L12.1635907,29.1778435 C13.1777805,29.1778435 14,30.2179187 14,31.5008287" id="Fill-14" fill="url(#linearGradient-6)"></path>
                        <path d="M13,41.156922 L13,42.1125957 C13,43.1549271 13.9396229,44 15.0987339,44 L22.1788114,44 C25.9460113,44 29,41.2535293 29,37.8656633 L29,33.5446546 C29,32.1393238 27.7331793,31 26.1704958,31 C24.607741,31 23.3409202,32.1393238 23.3409202,33.5446546 L23.3409202,37.2446166 C23.3409202,38.36295 22.3328456,39.2695177 21.0893655,39.2695177 L15.0987339,39.2695177 C13.9396229,39.2695177 13,40.1145264 13,41.156922" id="Fill-16" fill="url(#linearGradient-7)"></path>
                    </g>
                    <g id="编组" transform="translate(126.881, 107)">
                        <path d="M9.60701495,4.7232183 C9.8685759,7.39404424 8.9794208,9.67202572 8.9794208,9.67202572 C8.9794208,9.67202572 7.6660374,7.61982462 7.40447645,4.94890305 C7.14291549,2.27807711 8.03213399,0 8.03213399,0 C8.03213399,0 9.34545399,2.05229673 9.60701495,4.7232183" id="Fill-78" fill="url(#linearGradient-8)"></path>
                        <path d="M9.2404859,6.43799269 C10.433806,8.85311991 10.3906077,11.318328 10.3906077,11.318328 C10.3906077,11.318328 8.41260513,9.8679059 7.21928505,7.45277868 C6.02590029,5.03765146 6.06916324,2.57234727 6.06916324,2.57234727 C6.06916324,2.57234727 8.04716581,4.02286547 9.2404859,6.43799269" id="Fill-80" fill="url(#linearGradient-9)"></path>
                        <path d="M10.7804453,4.97036252 C10.4623264,7.6412568 9.07911597,9.67202572 9.07911597,9.67202572 C9.07911597,9.67202572 8.21173724,7.37255747 8.52992107,4.7016632 C8.84803999,2.03076892 10.2312504,0 10.2312504,0 C10.2312504,0 11.0985642,2.29946825 10.7804453,4.97036252" id="Fill-82" fill="url(#linearGradient-10)"></path>
                        <path d="M11.71767,6.90639837 C10.7156845,9.40673257 8.8524445,11.0096463 8.8524445,11.0096463 C8.8524445,11.0096463 8.61374059,8.55856606 9.61572604,6.05823186 C10.6176466,3.55789766 12.4808867,1.95498392 12.4808867,1.95498392 C12.4808867,1.95498392 12.7195906,4.40606417 11.71767,6.90639837" id="Fill-84" fill="url(#linearGradient-11)"></path>
                        <path d="M18.6666667,14.8681672 C18.6666667,20.1813977 14.4880107,24.488746 9.33333333,24.488746 C4.17872031,24.488746 0,20.1813977 0,14.8681672 C0,9.55484039 4.17872031,5.24758842 9.33333333,5.24758842 C14.4880107,5.24758842 18.6666667,9.55484039 18.6666667,14.8681672" id="Fill-86" fill="url(#linearGradient-12)"></path>
                    </g>
                    <g id="路径" transform="translate(10, 109.836)">
                        <polygon id="路径-9" fill="url(#linearGradient-13)" points="50.6137893 16.8606267 60 0 66.8490131 16.1639871"></polygon>
                        <polygon id="路径-9备份" fill="url(#linearGradient-14)" points="8.61378927 29.8606267 12.664534 14.0334282 24.8490131 29.1639871"></polygon>
                        <polygon id="路径-9备份-2" fill="url(#linearGradient-15)" points="99.6137893 17.8606267 103.664534 2.03342823 115.849013 17.1639871"></polygon>
                        <polygon id="路径-9备份-3" fill="url(#linearGradient-16)" points="138.613789 29.8606267 148.649656 13.6338117 154.849013 29.1639871"></polygon>
                        <mask id="mask-19" fill="white">
                            <use xlink:href="#path-18"></use>
                        </mask>
                        <use id="蒙版" fill="url(#linearGradient-17)" xlink:href="#path-18"></use>
                        <path d="M108.340717,28.1639871 C108.340717,28.1639871 110.780835,26.7997082 113.082856,25.0461365 C112.706169,26.0064611 112.823346,26.5673485 112.823346,26.5673485 C112.823346,26.5673485 114.858464,25.171845 116.512624,23.5225853 C122.037098,22.5248706 129.330924,19.543726 134.547671,16.02113 C141.219828,11.6818057 141.907975,8.16398714 135.838704,8.16398714 C129.769508,8.16398714 119.612279,11.6818057 113.397939,16.02113 C108.555784,19.5415453 107.612085,22.5211794 110.431435,23.5204913 C108.041889,24.9244452 106.589687,26.0631468 106.589687,26.0631468 C106.589687,26.0631468 107.652732,25.719468 109.242119,25.0941214 C107.026226,26.8285928 106,28.1639871 106,28.1639871 C106,28.1639871 107.601406,27.4064631 109.605076,26.2471712" id="形状结合" fill="url(#linearGradient-20)" opacity="0.699999988" mask="url(#mask-19)"></path>
                        <path d="M20.2881742,26.9088665 L20.1666906,26.7144693 C26.1625713,36.5106246 34.117008,42.2229979 42.4283424,42.2229979 C47.2879926,42.2229979 54.5269741,40.4420665 64.7099686,37.2708477 L49.5108059,53.2146588 L0.251856129,32.1473088 L20.2881742,26.9088665 Z M60.1852571,23.9035632 C65.5796442,23.9035632 72.5118863,22.8253469 80.2492398,20.9711524 L74.7036045,26.7878553 C65.9756222,28.614597 59.0708884,29.5644205 54.0362239,29.4441205 C46.4441622,29.2648839 41.3461408,26.5615077 39.334296,21.9306534 L49.8171307,19.1896473 C50.5185873,22.2218977 54.0190759,23.9035632 60.1852571,23.9035632 Z M82.553846,10.1639871 C85.5317048,10.1639871 87.8494013,10.5072807 89.3005936,11.0803859 L89.6771607,11.0799589 L89.5697473,11.1937677 C91.2490445,11.9490605 91.5443571,13.0543936 90.0485595,14.28586 C87.283478,16.5623075 79.4448976,18.4077328 72.5406001,18.4077328 C65.6363026,18.4077328 62.280805,16.5623075 65.0458865,14.28586 C67.810968,12.0094125 75.6495485,10.1639871 82.553846,10.1639871 Z" id="形状结合" fill="url(#linearGradient-21)" opacity="0.699999988" mask="url(#mask-19)"></path>
                        <path d="M-33.6916552,48.0210425 L-33.4677317,47.9172159 L-33.5324976,48.0210425 L-33.1964371,47.907478 C-32.5593998,47.6877382 -31.124683,47.1716399 -29.8395459,46.5586912 C-30.1592269,47.0316153 -30.0439168,47.3111679 -30.0439168,47.3111679 L-29.8030137,47.2054108 C-29.2574528,46.9609004 -27.9059564,46.3265275 -27.0162592,45.6641365 C-25.1155575,45.211515 -23.010592,44.510235 -21.3283792,43.7267693 L-7.10764447,37.1036732 C-5.5231016,36.3656959 -4.62680917,35.7006504 -4.49185571,35.2472384 C-2.6927682,34.7998996 -0.751858251,34.1435602 0.811941524,33.4152436 L24.1983757,22.5233591 L27.2773995,22.522973 C28.3181382,22.522973 28.0034201,23.0664855 26.5970782,23.7550683 L26.3985629,23.8498278 L21.1928788,26.2742978 C19.2156401,27.1951674 18.7211297,27.9417309 20.0885401,27.9417309 C21.4558882,27.9417309 24.1673349,27.1951674 26.1445736,26.2742978 L32.2240431,23.4428752 C36.9906217,21.2229119 38.1825453,19.4232351 34.8862454,19.4232351 L30.8547854,19.4232351 L34.7684449,17.6005063 C37.5585549,16.301054 38.1643017,15.2306629 36.1924269,15.166984 L36,15.1639871 C33.8000416,15.1639871 29.6457458,16.2604461 26.7684449,17.6005063 L7.17727476,26.7248029 L3.57212663,26.7248029 C0.0403757774,26.7248029 -7.23890038,28.7815709 -12.6865246,31.318721 L-19.6344553,34.5546166 C-21.8941856,35.6070525 -22.538421,36.4602267 -21.0733895,36.4602267 C-19.6083579,36.4602267 -16.5888189,35.6070525 -14.3290886,34.5546166 L-8.37964552,31.7837518 C-6.58146883,30.9462777 -4.17871036,30.2673755 -3.01291386,30.2673755 L-0.4302373,30.2678887 L-7.18805848,33.4152436 C-8.75185825,34.1435602 -9.62943707,34.7998996 -9.75163845,35.247376 C-11.563478,35.7006504 -13.5231016,36.3656959 -15.1076445,37.1036732 L-29.3283792,43.7267693 C-31.4414231,44.7108882 -32.3305517,45.5653336 -31.8224487,45.9611493 C-33.0841871,46.4252277 -33.9279391,46.8024043 -34.1819387,46.9189211 L-34.2623312,46.9562305 L-33.9108123,46.8858457 C-33.6850459,46.8394939 -33.3592656,46.7708234 -32.9592102,46.6815482 L-35.8345123,48.0210425 L-33.6916552,48.0210425 Z" fill="url(#linearGradient-22)" opacity="0.699999988" mask="url(#mask-19)"></path>
                    </g>
                    <path d="M94.0444166,25.6988719 C108.852895,24.6180393 119.435714,25.4048915 125.792874,28.0594286 C135.328615,32.0412343 130.895066,42.9002873 126.340097,42.6884161 C121.785128,42.4765449 117.289796,41.0612832 117.246537,36.1048613 C117.203278,31.1484394 125.853972,29.5389398 135.29448,36.3867732 C141.588151,40.9519955 142.007003,47.030567 136.551035,54.6224878" id="路径-7" stroke="#C7D0E7" stroke-width="0.8" stroke-dasharray="3,3" transform="translate(117.1978, 39.9261) scale(-1, 1) translate(-117.1978, -39.9261)"></path>
                    <g id="飞机" transform="translate(130.2827, 22.1721) scale(-1, 1) rotate(-24) translate(-130.2827, -22.1721)translate(115.0272, 10.321)">
                        <polygon id="路径" fill="url(#linearGradient-23)" points="17.9284846 16.7394775 21.9137895 14.1537801 21.9270151 19.1537801 17.9270151 16.7394775"></polygon>
                        <path d="M28.623685,8.80210825 C30.0503837,9.1849987 28.6148235,10.3151661 28.6148235,10.3151661 L23.7823616,13.6729303 L8.73556408,6.25852001 C8.73556408,6.25852001 18.8982078,12.7747746 21.9997267,14.7945573 L11.5225004,21.3947916 C9.86540312,22.3014876 9.43119047,21.5769848 9.43119047,21.5769848 L8.70159507,20.1365196 L1.31259546,3.02459026 C1.03493567,2.43246227 1.81622306,1.75208445 2.65067934,1.86310844 L28.6207311,8.80210825 L28.623685,8.80210825 Z" id="形状" fill="url(#linearGradient-24)" transform="translate(15.2555, 11.8511) rotate(-8) translate(-15.2555, -11.8511)"></path>
                        <polygon id="路径-8" fill="url(#linearGradient-25)" points="7.30275621 6.8201817 23.9529278 12.4684884 21.9270151 19.1537801 20.9839444 14.9827857"></polygon>
                    </g>
                    <path d="M90,98.1305589 C96.627417,98.1305589 102,103.503142 102,110.130559 C102,116.757976 96.627417,122.130559 90,122.130559 C83.372583,122.130559 78,116.757976 78,110.130559 C78,103.503142 83.372583,98.1305589 90,98.1305589 Z M90,77.75 C101.844757,77.75 112.871069,82.884452 120.492832,91.6473798 C122.667514,94.1476645 122.403561,97.9374735 119.903276,100.112155 C117.402991,102.286837 113.613182,102.022884 111.438501,99.5225989 C106.072337,93.3529898 98.3348635,89.75 90,89.75 C81.6651365,89.75 73.9276627,93.3529898 68.5614994,99.5225989 C66.3868177,102.022884 62.5970086,102.286837 60.0967239,100.112155 C57.5964392,97.9374735 57.3324859,94.1476645 59.5071676,91.6473798 C67.1289311,82.884452 78.1552428,77.75 90,77.75 Z M90,54.95 C106.578732,54.95 122.050094,61.9269914 133.005299,73.934898 C135.238682,76.38289 135.064707,80.1778942 132.616715,82.4112771 C130.168723,84.64466 126.373719,84.470685 124.140336,82.022693 C115.43565,72.4815612 103.169506,66.95 90,66.95 C76.8309104,66.95 64.5651181,72.4812089 55.8604658,82.0218143 C53.6270203,84.4697492 49.8320117,84.6436272 47.3840768,82.4101817 C44.9361419,80.1767362 44.7622639,76.3817275 46.9957094,73.9337927 C57.950873,61.9265477 73.421792,54.95 90,54.95 Z M90,32.35 C111.284536,32.35 131.170205,41.1760136 145.411968,56.4277344 C147.673551,58.8496978 147.543537,62.6464623 145.121574,64.9080452 C142.69961,67.1696281 138.902846,67.039614 136.641263,64.6176505 C124.645909,51.7716439 107.92439,44.35 90,44.35 C72.0756099,44.35 55.354091,51.7716439 43.358737,64.6176505 C41.0971541,67.039614 37.3003897,67.1696281 34.8784262,64.9080452 C32.4564628,62.6464623 32.3264487,58.8496978 34.5880316,56.4277344 C48.8297953,41.1760136 68.7154641,32.35 90,32.35 Z" id="形状结合" fill="url(#linearGradient-26)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>