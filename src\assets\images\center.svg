<?xml version="1.0" encoding="UTF-8"?>
<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon-Server Host</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="12" height="12"></rect>
        <rect id="path-3" x="0" y="0" width="12" height="12"></rect>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="12" height="12" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
        <mask id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="12" height="12" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
    </defs>
    <g id="PC客户端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="成员-有成员" transform="translate(-188, -120)">
            <g id="Frame-8" transform="translate(72, 24)">
                <g id="Frame-148" transform="translate(16, 86)">
                    <g id="Frame-157" transform="translate(30, 6)">
                        <g id="icon-Server-Host" transform="translate(70, 4)">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#path-1"></use>
                            </mask>
                            <g id="icon-Server-Host-(Background/Mask)"></g>
                            <g mask="url(#mask-2)">
                                <mask id="mask-5" fill="white">
                                    <use xlink:href="#path-3"></use>
                                </mask>
                                <g id="icon-Server-Host-(Background/Mask)" stroke="#3C78FF" mask="url(#mask-4)" stroke-dasharray="0,0">
                                    <use mask="url(#mask-6)" xlink:href="#path-3"></use>
                                </g>
                                <path d="M5.14239284,2.14257812 L6.85667875,2.14257812 C7.09337222,2.14257812 7.28525019,2.33445609 7.28525019,2.57114956 L7.28525019,4.28543547 C7.28525019,4.52212893 7.09337222,4.7140069 6.85667875,4.7140069 L5.14239284,4.7140069 C4.90569938,4.7140069 4.71382141,4.52212893 4.71382141,4.28543547 L4.71382141,2.57114956 C4.71382141,2.33445609 4.90569938,2.14257812 5.14239284,2.14257812 Z" id="Rectangle-410" fill="#3C78FF" mask="url(#mask-5)"></path>
                                <path d="M7.71387264,7.28515625 L9.42815855,7.28515625 C9.66485202,7.28515625 9.85672998,7.47703422 9.85672998,7.71372768 L9.85672998,9.42801359 C9.85672998,9.66470706 9.66485202,9.85658503 9.42815855,9.85658503 L7.71387264,9.85658503 C7.47717917,9.85658503 7.28530121,9.66470706 7.28530121,9.42801359 L7.28530121,7.71372768 C7.28530121,7.47703422 7.47717917,7.28515625 7.71387264,7.28515625 Z" id="Rectangle-412" fill="#3C78FF" mask="url(#mask-5)"></path>
                                <path d="M2.57127926,7.28515625 L4.28556517,7.28515625 C4.52225863,7.28515625 4.7141366,7.47703422 4.7141366,7.71372768 L4.7141366,9.42801359 C4.7141366,9.66470706 4.52225863,9.85658503 4.28556517,9.85658503 L2.57127926,9.85658503 C2.33458579,9.85658503 2.14270782,9.66470706 2.14270782,9.42801359 L2.14270782,7.71372768 C2.14270782,7.47703422 2.33458579,7.28515625 2.57127926,7.28515625 Z" id="Rectangle-413" fill="#3C78FF" mask="url(#mask-5)"></path>
                                <polyline id="路径-5" stroke="#3C78FF" stroke-width="0.5" stroke-linejoin="round" mask="url(#mask-5)" points="3.42842221 7.28515625 3.42842221 6 8.52423784 6 8.52423784 7.28515625"></polyline>
                                <line x1="6" y1="4.7140069" x2="6" y2="6" id="路径-6" stroke="#3C78FF" stroke-width="0.5" mask="url(#mask-5)"></line>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>