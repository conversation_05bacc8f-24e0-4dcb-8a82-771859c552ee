import {defineStore} from 'pinia';
import mqtt, {MqttClient} from 'mqtt';
import {ref} from "vue";
import {mqttUrl} from '../../api/config.ts'
import {invoke} from "@tauri-apps/api/tauri";
import {ElMessage} from "element-plus";

let trafficTimer = ref<any>(null);
// 缓存对象，用于存储消息的最后发送时间
// const messageCache: Record<string, number> = {};
export const useMqttStore = defineStore('mqtt', {
    state: () => ({
        isConnected: false, // MQTT 连接状态
        message: null, // 接收到的 MQTT 消息
        client: null as MqttClient | null, // MQTT 客户端实例
        clientName: '', dna: '', wgConfig: {} as any, chartData: [], currentTrafficData: {
            up: '0bps', down: '0bps',
        },
        loading: null as any | null,
        newInfo:{level:6}
    }), actions: {
        setClientName(name: any) {
            this.clientName = name;
        }, setLoading(loading: any) {
            this.loading = loading;
        },setWgConfig() {
            this.wgConfig = {
                level: Math.random() * 100,
            }
        }, setDna() {
            this.dna = ''
        },  stopReportThree() {
            console.log('停止三级组网在线状态上报')
            clearInterval(trafficTimer.value)
            trafficTimer.value = null
        }, startReportThree() {
            let sendConfig = {
                cmd: 'status', data: {}
            }
            console.log('开启三级组网在线状态上报', sendConfig)
            // 首次触发时先清除旧定时器
            let num = 0
            this.publishCmd(JSON.stringify(sendConfig))
            // 清除旧定时器
            if (trafficTimer.value) {
                clearInterval(trafficTimer.value);
                trafficTimer.value = null;
            }
            // 设置新定时器，避免重复创建
            trafficTimer.value = setInterval(() => {
                num = num + 10
                if (num % 300 === 0) {
                    console.log('二级组网在线状态上报', sendConfig)
                    this.publishCmd(JSON.stringify(sendConfig))
                }

            }, 10000);
        }, setChartData(chartData: any) {
            this.chartData = chartData;
        }, setCurrentTrafficData(currentTrafficData: any) {
            this.currentTrafficData = currentTrafficData;
        }, async resetVirtualAdapter() {
            const osType = await invoke<string>('get_os_type');
            console.log('当前操作系统:', osType);  // 输出 "windows" 或 "macos" 等
            if (osType !== 'windows') {
                this.publishCmd(JSON.stringify({
                    cmd: 'config', data: {}
                }))
                return
            }

            const adapters: any[] = await invoke('get_network_adapters_all');
            const targetAdapter = adapters.find(adapter => adapter.name == 'usr' && adapter.description.includes('TAP-Windows') && adapter.ipv4 == '');
            if (targetAdapter) {
                console.log('适配器已存在:', targetAdapter);
                const macAddress = targetAdapter.mac.replace(/-/g, ':')
                this.publishCmd(JSON.stringify({
                    cmd: 'config', data: {
                        mac: macAddress
                    }
                }))
                return; // 存在则结束函数n
            } else {
                const usrAdapter = adapters.find(adapter => adapter.description.includes('TAP-Windows') && adapter.name == 'usr'&& adapter.ipv4 != '');
                if (usrAdapter) {
                    await invoke('reset_network', {
                        adapter: 'usr',
                    })
                    const macAddress = usrAdapter.mac.replace(/-/g, ':')
                    this.publishCmd(JSON.stringify({
                        cmd: 'config', data: {
                            mac: macAddress
                        }
                    }))
                    // ElMessage.error('网卡被占用')
                    return
                }
                const spaceAdapter = adapters.find(adapter => adapter.description.includes('TAP-Windows') && adapter.ipv4 == '');
                if (spaceAdapter) {
                    const command = `Rename-NetAdapter -Name "${spaceAdapter.name}" -NewName "usr"`;
                    const output = await invoke('run_powershell', {command});
                    console.log(`适配器 ${spaceAdapter.name} 重命名成功:`, output);
                    const macAddress = spaceAdapter.mac.replace(/-/g, ':')
                    this.publishCmd(JSON.stringify({
                        cmd: 'config', data: {
                            mac: macAddress
                        }
                    }))
                } else {
                    this.publishCmd(JSON.stringify({
                        cmd: 'config', data: {}
                    }))
                }
            }
        }, // 连接 MQTT
        connect() {
            if (this.client != null) {
                return
            }
            //  测试版本
            const client = mqtt.connect(mqttUrl, {
                reconnectPeriod: 1000, keepalive: 30, connectTimeout: 4000, // 认证信息
                clientId: 'windows-' + Math.random().toString(36).substring(7),
                username:'client',
                password:'client12333',
            });
            client.on('connect', () => {
                this.isConnected = true;
                this.client = client;
                console.log('MQTT 连接成功');
                this.subscribe(`/sdw/${this.clientName}/d`)
                setTimeout(() => {
                    this.resetVirtualAdapter()
                }, 1000)
            });
            client.on('message', async (_topic, payload) => {
                const str = payload.toString()
                console.log('收到消息:', str);
                const info: any = JSON.parse(str)
                console.log('收到消息:', info);
                this.newInfo = info.data
                if (info.cmd == 'config') {
                    if (info.dna == '') {
                        this.setConfig({
                            dna: '',
                            data:{
                                level: Math.random() * 100,
                            }
                        })
                        this.setCurrentTrafficData({
                            up: '0bps', down: '0bps',
                        })
                        this.stopReportThree()
                    } else {
                        console.log('收到dna配置信息', info.dna, this.dna);
                        if (info.dna == this.dna) {
                            //这里应该增加判断是否连接的逻辑

                        } else {
                            if (info.data.level == 2) {
                                if(!info.data.version || info.data.version == 0){
                                    ElMessage.error('该版本不支持旧版二层组网，请联系技术支持')
                                    return
                                }
                                const osType = await invoke<string>('get_os_type');
                                console.log('当前操作系统:', osType);  // 输出 "windows" 或 "macos" 等
                                if (osType !== 'windows') {
                                    this.setCurrentTrafficData({
                                        up: '0bps', down: '0bps',
                                    })
                                    this.setChartData([])
                                    if(this.newInfo.level == 2){
                                        this.setConfig(info)
                                    }
                                    return
                                }
                                
                                const adapters: any[] = await invoke('get_network_adapters_all');
                                console.log('获取所有网卡', adapters);
                                const targetAdapter = adapters.find(adapter => adapter.name == 'usr' && adapter.description.includes('TAP-Windows'));
                                console.log('寻找指定网卡', targetAdapter);
                                if (!targetAdapter) {
                                    console.log('没有找到网卡');
                                    ElMessage.error('没有找到网卡，请检查网卡是否安装')
                                } else {
                                    this.setCurrentTrafficData({
                                        up: '0bps', down: '0bps',
                                    })
                                    this.setChartData([])
                                    if(this.newInfo.level == 2){
                                        this.setConfig(info)
                                    }
                                }
                            }
                            if (info.data.level == 3) {
                                this.setCurrentTrafficData({
                                    up: '0bps', down: '0bps',
                                })
                                this.setChartData([])
                                this.setConfig(info)
                            }
                        }
                    }
                }
            });
            client.on('disconnect', () => {
                console.log('mqtt 断开:');
            });
            client.on('reconnect', () => {
                console.log('mqtt reconnect:');
            });
            client.on('offline', () => {
                console.log('mqtt offline:');
            });
            client.on('close', () => {
                console.log('mqtt close:');
            });
            client.on('error', (error) => {
                console.error('MQTT 连接错误:', error);
            });
        }, // 断开 MQTT
        setConfig(info: any) {
            this.dna = info.dna
            this.wgConfig = {
                level: Math.random() * 100,
            }
            this.stopReportThree()
            setTimeout(() => {
                console.log('dna-end',);
                this.wgConfig = info.data;
            }, 4000)
        }, disconnect() {
            if (this.client) {
                this.client.end();
                this.isConnected = false;
                this.client = null;
                console.log('MQTT 已断开');
            }
        }, // 订阅主题
        subscribe(topic: any) {
            if (this.client) {
                this.client.subscribe(topic, (err) => {
                    if (!err) console.log(`已订阅主题: ${topic}`);
                });
            }
        }, // 上报内容
        async publishCmd(message: string) {

            if (message.indexOf('traffic') != -1) {
                // const now = Date.now();
                // const lastPublishedTime = messageCache[message];
                // // 如果消息在 3 秒内已经发送过，则跳过
                // if (lastPublishedTime && now - lastPublishedTime < 10000) {
                //     console.log(message, '跳过重复命令');
                //     return;
                // }
                // // 记录当前消息的发送时间
                // messageCache[message] = now;
                console.log(message, '上报信息');
                if (this.client) {
                    this.client.publish(`/sdw/${this.clientName}/u`, message, {qos: 1});
                }
            } else {
                console.log(message, '上报信息');
                if (this.client) {
                    let publicRes = await this.client.publishAsync(`/sdw/${this.clientName}/u`, message);
                    console.log('publicRes', publicRes,message);
                }
            }
        },
    },
});

