<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="12px" viewBox="0 0 20 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>转发备份</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="20" height="12" rx="1.5"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="12" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="PC客户端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="成员-有成员" transform="translate(-171, -224)">
            <g id="Frame-8" transform="translate(72, 24)">
                <g id="Frame-148" transform="translate(16, 86)">
                    <g id="Frame-144" transform="translate(0, 104)">
                        <g id="Frame-157" transform="translate(30, 6)">
                            <g id="转发备份" transform="translate(53, 4)">
                                <use id="矩形" stroke="#00D13F" mask="url(#mask-2)" stroke-width="1.2" stroke-dasharray="0,0" xlink:href="#path-1"></use>
                                <text id="转发" font-family="PingFangHK-Regular, PingFang HK" font-size="7" font-weight="normal" line-spacing="8" fill="#00D13F">
                                    <tspan x="3" y="8.5">转发</tspan>
                                </text>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>