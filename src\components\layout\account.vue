<template>
  <div class="update-container" @click.stop>
    <div class="updateTitle">切换账号</div>
    <div class="accountList">
      <div class="accountItem" v-for="(item,index) in list">
        <div class="accountAvatar" :style="{ backgroundColor: getColor(index) }">{{ getFirstName(item.name) }}</div>
        <div class="accountLeft">
          <div class="accountName">{{item.name}}</div>
          <div class="accountClient">{{item.clientName}}</div>
        </div>
        <div class="accountDesc">
          <div class="accountCurrent" v-if="item.currentAccount">当前账号</div>
          <div class="accountText" v-else @click="accountSelect(item)">切换账号</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {useRouter} from "vue-router";
import {useMqttStore, useN3nStore, useTokenStore} from "../../store";
import {invoke} from "@tauri-apps/api/tauri";
import {logoutAPI} from "../../api/UserApi.ts";

const tokenStore = useTokenStore()
const mqttStore = useMqttStore()
const n3nStore: any = useN3nStore()
const router = useRouter()
const getFirstName = (name: string) => {
  if(name){
    return name.slice(0, 1)||''
  }else{
    return ''
  }
}
/**
 * 根据数组顺序，每三个一组设置颜色，根据传进来的index 返回对应的颜色
 */
const getColor = (index: number) => {
  const colors = ['#58CD1E', '#F9AB34', '#3C78FF'];
  return colors[index % colors.length];
}
interface User {
  name: string
  clientName: string
  currentAccount: boolean
}
const list = ref<User[]>([])
const emits = defineEmits(['close'])
/**
 * 账号选择
 */
const accountSelect = (item: User) => {
  const updatedUsers = list.value.map(user  => ({
    ...user,
    currentAccount: user.clientName === item.clientName
  }))
  emits('close')
  localStorage.setItem('userList', JSON.stringify(updatedUsers))
  disableTunnel()
}
/**
 * 关闭组网（二层/三层）
 */
async function disableTunnel(): Promise<void> {
  try {
    await invoke('disable_wiresock')
  } catch (error) {
    console.error('禁用失败:', error)
  }
  if (mqttStore.wgConfig.level == 2) {
    try {
      let resStop = await invoke('stop_vpn')
      console.log(resStop, 'stop_vpn')
      // 清除网关
      let result = await invoke('reset_network', {
        adapter: n3nStore.network.name,
      })
      // await invoke('stop_monitor')
      console.log(result, 'stop_monitor')
    } catch (error) {
      console.log('禁用失败:', error)
    }
  }
  mqttStore.setChartData([])
  mqttStore.setWgConfig()
  mqttStore.disconnect()
  let res:any = await logoutAPI()
  if (res.msg === 'success') {
    tokenStore.exitToken()
    router.push({path: '/login'})
  }
}
onMounted(() => {
  const str = localStorage.getItem('userList')||'[]'
  const arr = JSON.parse(str)
  //  arr数组排序currentAccount 为true的在前边
  arr.sort((a: any, b: any) => {
    if (a.currentAccount) {
      return -1
    }
    if (b.currentAccount) {
      return 1
    }
    return 0
  })
  list.value =arr
})
</script>

<style scoped lang="less">
.update-container {
  width: 252px;
  border-radius: 6px;
  background: var(--usr-white);
  padding: 24px;
  height: 252px;
  display: flex;
  flex-direction: column;
  .updateTitle{
    font-family: PingFangHK-Medium;
    font-weight: 500;
    font-size: 20px;
    color:var(--usr-btn-border-text);
    letter-spacing: 0;
    text-align: center;
    margin-top: 8px;
    margin-bottom: 24px;
  }
  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none;
  }
  .accountList{
    flex: 1;
    overflow-y: scroll;
  }
  .accountItem{
    width: 221px;
    height: 32px;
    background: var(--usr-menu-hover);
    display: flex;
    align-items: center;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 8px;
    .accountAvatar{
      width: 32px;
      height: 32px;
      border-radius: 16px;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 13.33px;
      color: var(--usr-white);
      letter-spacing: 0;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
    }
    .accountLeft{
      flex:1 ;
      .accountName{
        font-family: PingFangHK-Medium;
        font-weight: 500;
        font-size: 13px;
        color: var(--usr-btn-border-text);
        letter-spacing: 0;
        line-height: 22px;
      }
      .accountClient{
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 12px;
        color: var(--usr-account-text);
        letter-spacing: 0;
        line-height: 20px;
      }
    }
    .accountDesc{
      cursor: pointer;
      .accountCurrent{
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 12px;
        color: var(--usr-account-current);
        letter-spacing: 0;
        text-align: right;
        line-height: 20px;
      }
      .accountText{
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 12px;
        color: var(--usr-account-change);
        letter-spacing: 0;
        text-align: right;
        line-height: 20px;
      }
    }
  }

}

</style>
