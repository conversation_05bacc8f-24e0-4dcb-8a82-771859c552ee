<template>
  <div class="layout-admin-wrapper">
    <!-- SubMenu -->
    <LayoutSideBar/>
    <div class="app-main-container">
      <router-view></router-view>
    </div>
  </div>
  <tool></tool>
</template>
<script lang="ts" setup>
import LayoutSideBar from './LayoutSideBar.vue'
import tool from './tool.vue'
</script>
<style scoped>
.layout-admin-wrapper {
  display: flex;
  height: 100vh;
}

.app-main-container {
  flex: 1;
  overflow: hidden;
}
</style>
