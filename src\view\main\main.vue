<script lang="ts" setup>
import {onMounted, reactive, ref, watch} from "vue";
import {ElLoading, ElMessage, FormInstance, FormRules} from 'element-plus'
import IndexCard from '../../components/main/index.vue'
// 在顶部导入图标
import {getAccountInfoAPI, modifyNameAPI, modifyPasswordAPI} from "../../api/network.ts";
import {useRouter} from "vue-router";
import {useMqttStore} from "../../store";
const mqttStore = useMqttStore()
// 在 setup 中添加响应式状态
const showPassword = ref(false)
const firstTimer: any = ref(null)
const loading: any = ref(null)
const router = useRouter()
import { useRoute } from 'vue-router'

const route = useRoute()
const getToken = () => {
  return sessionStorage.getItem('token') || ''
}
watch(() => route.path, (newVal, oldVal) => {
  if (newVal != oldVal) { // 路由切换时触发
    clearInterval(firstTimer.value)
    firstTimer.value = null
  }
})
onMounted(async () => {
  clearInterval(firstTimer.value)
  firstTimer.value = null
  let num = 0
  loading.value = ElLoading.service({
    lock: true,
    text: '加载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  mqttStore.setLoading(loading.value)
  dealResetLogin()
  firstTimer.value = setInterval(() => {
    num++
    if (num > 10) {
      loading.value.close()
      ElMessage.error('网络错误，请登录重试')
      clearInterval(firstTimer.value)
      firstTimer.value = null
      router.push('/login')
      return
    }
    let token = getToken()
    if(token){
      dealResetLogin()
    }else{
      loading.value.close()
      clearInterval(firstTimer.value)
      firstTimer.value = null
    }
  }, 3000)
})
const dialogVisible: any = ref(9999)
/**
 * 判断显示初始化设置个人信息，还是展示首页
 * 更新当前用户的用户名
 */
const dealResetLogin = async () => {
  let res: any = await getAccountInfoAPI()
  if (res.msg === 'success') {
    loading.value.close()
    clearInterval(firstTimer.value)
    firstTimer.value = null
    dialogVisible.value = res.result.forceChangePwd
    let list: any = localStorage.getItem('userList')
    list = JSON.parse(list)
    for (let i in list) {
      if (list[i].currentAccount == true) {
        list[i].name = res.result.name + ''
      }
    }
    localStorage.setItem('userList', JSON.stringify(list))
  }
}
/**
 * 定义个人信息
 */
const form = reactive({
  name: '',
  password: '',
  rePassword: '',
})

const ruleFormRef = ref<FormInstance>()
/**
 * 姓名检查
 */
const checkName = (_rule: any, value: any, callback: any) => {
  // 校验值必须为1-30字符
  if (value.length < 1 || value.length > 30) {
    return callback(new Error('请输入30字符以内的姓名'))
  } else {
    callback()
  }
}
/**
 * 密码检查
 */
const validatePassword = (_rule: any, value: any, callback: any) => {
  // 校验密码必须6-30位
  if (value.length < 6 || value.length > 30) {
    return callback(new Error('请输入6-30位密码'))
  } else {
    callback()
  }
}
/**
 * 密码检查,以及两次密码是否一致
 */
const validateRePassword = (_rule: any, value: any, callback: any) => {
  if (value.length < 6 || value.length > 30) {
    return callback(new Error('请再次输入6-30位密码'))
  } else if (value !== form.password) {
    callback(new Error("两次密码输入不一致!"))
  } else {
    callback()
  }
}
/**
 * 表单规则
 */
const rules = reactive<FormRules<typeof form>>({
  password: [{validator: validatePassword, trigger: 'blur'}],
  rePassword: [{validator: validateRePassword, trigger: 'blur'}],
  name: [{validator: checkName, trigger: 'blur'}],
})
/**
 * 提交个人信息
 */
const submitFlag = ref(false)
const submitForm = (ruleFormRef: FormInstance | undefined) => {
  if (!ruleFormRef) return
  ruleFormRef.validate(async (valid) => {
    if (valid) {
      if (submitFlag.value) {
        return
      }
      submitFlag.value = true
      console.log('submit!')
      let resName: any = await modifyNameAPI({
        name: form.name + ''
      })
      if (resName.msg === 'success') {
        let oldPassword = ''
        let list: any = localStorage.getItem('userList')
        list = JSON.parse(list)
        for (let i in list) {
          if (list[i].currentAccount == true) {
            oldPassword = list[i].password
            list[i].name = form.name + ''
          }
        }
        let res: any = await modifyPasswordAPI({
          oldPassword,
          password: form.password
        })
        if (res.msg === 'success') {
          dialogVisible.value = false
          for (let i in list) {
            if (list[i].currentAccount == true) {
              list[i].password = form.password
            }
          }
          localStorage.setItem('userList', JSON.stringify(list))
          mainPageRef.value.refreshAccountInfo()
          dealResetLogin()
        }
      }
      submitFlag.value = false
    } else {
      console.log('error submit!')
    }
  })
}
const mainPageRef: any = ref(null)
</script>

<template>
  <div
    v-if="dialogVisible==true"
    class="maskBox"
  >

    <div class="maskContent">
      <div class="maskTitle">设置</div>
      <div class="formBox">
        <el-form ref="ruleFormRef"
                 :model="form"
                 :rules="rules"
                 label-width="0">
          <el-form-item label=" " prop="name">
            <el-input v-model.number="form.name" placeholder="请输入姓名">
              <template #prepend>
                  <img class="icon" src="../../assets/images/userName.svg" alt="">
              </template>
            </el-input>
          </el-form-item>
          <!-- 修改密码输入框 -->
          <el-form-item label=" " prop="password">
            <el-input v-model="form.password" :type="showPassword ? 'text' : 'password'" autocomplete="off"
                      placeholder="请输入密码" show-password>
              <template #prepend>
                <img class="icon" src="../../assets/images/passwordA.svg" alt="">
              </template>
            </el-input>
          </el-form-item>
          <!-- 修改确认密码输入框 -->
          <el-form-item label=" " prop="rePassword">
            <el-input v-model="form.rePassword" :type="showPassword ? 'text' : 'password'" autocomplete="off"
                      placeholder="请重复输入密码" show-password>
              <template #prepend>
                <img class="icon" src="../../assets/images/passwordA.svg" alt="">
              </template>
            </el-input>
          </el-form-item>
          <baseBtn text="确定" :length="236"   @click="submitForm(ruleFormRef)"></baseBtn>
        </el-form>
      </div>
    </div>
  </div>
  <IndexCard v-if="dialogVisible==false" ref="mainPageRef"></IndexCard>
</template>
<style lang="less" scoped>
.maskBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("../../assets/images/loginBg.png");
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;

  .maskContent {
    width: 236px;
    background: var(--usr-white);
    box-shadow: 0 4px 10px 0 var(--usr-shadow);
    border-radius: 6px;
    padding: 32px;
    min-height: 268px;

    .maskTitle {
      height: 28px;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 20px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      text-align: center;
    }

    .formBox {
      margin-top: 20px;
      width: 236px;
    }
  }

}
.icon{
  width: 15px;
  height: 16px;
}

/* 深度选择器穿透 */
:deep(.el-form-item__label) {
padding: 0!important;
}
:deep(.el-input__inner) {
  border-radius: 0;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  color: var(--usr-input-text) !important;
}

// 调整密码框图标位置
:deep(.el-input__wrapper) {
  border-radius: var(--Radius-border-radius-none, 0px) var(--Radius-border-radius-base, 4px) var(--Radius-border-radius-base, 4px) var(--Radius-border-radius-none, 0px);
  border: 1px solid var(--usr-input-border);
  background: var(--usr-white);
  box-shadow: none;
  border-left: 0!important;
  padding-left: 0;
}

// 调整密码框图标位置
:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
}

:deep(.el-input-group__prepend) {
  padding: 0 10px 0 18px !important;
  border-radius: var(--Radius-border-radius-base, 4px) var(--Radius-border-radius-none, 0px) var(--Radius-border-radius-none, 0px) var(--Radius-border-radius-base, 4px);
  border: 1px solid var(--usr-input-border);
  background: var(--usr-white);
  box-shadow: none;
  border-right: 0!important;
}
:deep(.el-input__inner::placeholder) {
  color: var(--usr-input-placeholder) !important;
}
</style>
