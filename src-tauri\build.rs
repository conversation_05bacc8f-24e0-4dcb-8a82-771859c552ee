
fn main() -> Result<(), Box<dyn std::error::Error>> {
    #[cfg(not(target_os = "windows"))]{
        // compiling protos using path on build time
        let mut config = prost_build::Config::new();
        // enable optional fields
        config.protoc_arg("--experimental_allow_proto3_optional");
        // make sure empty DNS is deserialized correctly as None
        config.type_attribute(".DeviceConfig", "#[serde_as]");
        config.field_attribute(
            ".DeviceConfig.dns",
            "#[serde_as(deserialize_as = \"NoneAsEmptyString\")]",
        );
        // Make all messages serde-serializable
        config.type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]");
        tonic_build::configure().compile_protos_with_config(
            config,
            &["proto/client/client.proto", "proto/core/proxy.proto"],
            &["proto/client", "proto/core"],
        )?;
        tauri_build::build();
    }
    #[cfg(target_os = "windows")]{
        let mut windows = tauri_build::WindowsAttributes::new();
        windows = windows.app_manifest(r#"
            <assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
                <dependency>
                    <dependentAssembly>
                        <assemblyIdentity
                            type="win32"
                            name="Microsoft.Windows.Common-Controls"
                            version="6.0.0.0"
                            processorArchitecture="*"
                            publicKeyToken="6595b64144ccf1df"
                            language="*"
                        />
                    </dependentAssembly>
                </dependency>
                <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
                    <security>
                        <requestedPrivileges>
                            <requestedExecutionLevel
                                level="requireAdministrator"
                                uiAccess="false"
                            />
                        </requestedPrivileges>
                    </security>
                </trustInfo>
            </assembly>
        "#);
        tauri_build::try_build(
            tauri_build::Attributes::new()
                .windows_attributes(windows)
        ).expect("Failed to set admin manifest");
    }

    Ok(())
}
