import { defineStore } from 'pinia'
import { reactive, toRefs } from 'vue'

export const useN3nStore = defineStore('n3n', () => {
  const initData: any = reactive({
    status: false,
    network: {},
    wgConfig: {}
  })
  // 设置store中记录的整体路由
  function setWgConfig(wgConfig: any) {
    initData.wgConfig = wgConfig
  }
  function setStatus(status: any) {
    initData.status = status
  }
  function setNetwork(network: any) {
    initData.network = network
  }
  return { ...toRefs(initData), setNetwork,setWgConfig,setStatus }
},{persist: true})
