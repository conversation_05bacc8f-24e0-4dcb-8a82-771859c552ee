<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="180px" viewBox="0 0 180 180" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>xxxhdpi/缺省暂无终端</title>
    <defs>
        <linearGradient x1="45.2891013%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-1">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#D3DDF5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="54.3549543%" y1="13.1370911%" x2="48.6576509%" y2="39.5715228%" id="linearGradient-2">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#D3DDF5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.9863336%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-3">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-5.40761319%" y1="64.0064261%" x2="51.7799905%" y2="43.1716043%" id="linearGradient-4">
            <stop stop-color="#F6F9FF" offset="0%"></stop>
            <stop stop-color="#F1F6FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="67.1700652%" y1="13.1370911%" x2="44.7075859%" y2="39.5715228%" id="linearGradient-5">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#D3DDF5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="14.2512996%" x2="50%" y2="88.9646581%" id="linearGradient-6">
            <stop stop-color="#E8EEFD" offset="0%"></stop>
            <stop stop-color="#AFBCDF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="12.6383167%" y1="42.366167%" x2="208.800807%" y2="42.366167%" id="linearGradient-7">
            <stop stop-color="#E8EEFD" offset="0%"></stop>
            <stop stop-color="#AFBCDF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="12.6383167%" y1="48.253887%" x2="151.386088%" y2="69.1401099%" id="linearGradient-8">
            <stop stop-color="#D0DAF1" offset="0%"></stop>
            <stop stop-color="#96A4CB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="49.076087%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-9">
            <stop stop-color="#E8EEFD" offset="0%"></stop>
            <stop stop-color="#AFBCDF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.3161686%" y1="11.7433646%" x2="50%" y2="91.6982073%" id="linearGradient-10">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#D3DDF5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="13.6421406%" x2="50%" y2="89.6286172%" id="linearGradient-11">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="31.290762%" y1="11.7829834%" x2="50%" y2="91.6550244%" id="linearGradient-12">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-13">
            <stop stop-color="#EAF1FF" offset="11.2333711%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="60.7368096%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <path d="M80,12.1305589 C124.18278,12.1305589 160,19.2940029 160,28.1305589 L160,50.1305589 L0,50.1305589 L0,28.1305589 L0.0107177143,27.8659696 C0.717180956,19.1514504 36.2590478,12.1305589 80,12.1305589 Z" id="path-14"></path>
        <linearGradient x1="50%" y1="-6.41910769%" x2="50%" y2="98.6257202%" id="linearGradient-16">
            <stop stop-color="#C2D0F3" offset="0%"></stop>
            <stop stop-color="#F1F4FA" offset="42.4331772%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="PC客户端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="成员-无任何终端" transform="translate(-483, -224)">
            <g id="Frame-9" transform="translate(262, 24)">
                <g id="缺省暂无终端" transform="translate(221, 200)">
                    <rect id="矩形" x="0" y="0" width="180" height="180"></rect>
                    <g id="编组-5" transform="translate(37, 108)">
                        <polygon id="Fill-17备份" fill="url(#linearGradient-1)" points="0 31 15.5555556 31 15.5555556 -7.10542736e-15 2.48258204e-15 -7.10542736e-15"></polygon>
                        <polygon id="Fill-18备份" fill="url(#linearGradient-2)" points="14.9206349 31 20 31 20 -7.10542736e-15 14.9206349 -7.10542736e-15"></polygon>
                        <polygon id="Fill-27备份-3" fill="url(#linearGradient-3)" points="50 40 51 40 51 3 50 3"></polygon>
                    </g>
                    <path d="M25.2647194,69.0548926 C26.3150758,66.9214766 30.1284133,69.421633 35.082119,71.9985264 C40.0358246,74.5754198 44.2540309,76.253233 43.2036744,78.386649 C42.153318,80.520065 37.2860641,80.1605556 32.3323584,77.5836622 C27.3786528,75.0067688 24.214363,71.1883086 25.2647194,69.0548926 Z M120.300271,53.0468191 C107.989982,69.9898777 156.033378,52.627551 149.984886,65.2976836 C143.936394,77.9678161 8.1323805,76.1121838 36.0001067,56.8525039 C52.3130504,45.9958229 26.3451606,53.7592584 24.2774483,46.4724847 C22.209736,39.1857109 44.2632758,35.4629546 64.5772192,35.0476402 C84.8911625,34.6323258 132.61056,36.1037605 120.300271,53.0468191 Z M144.022076,42.6555007 C153.692311,42.5682792 161.635715,45.8345422 161.764167,49.9509035 C161.863109,53.1216141 157.219982,53.4212689 150.661999,53.4314729 L149.611028,53.4310551 C148.663419,53.4292146 147.681021,53.424349 146.671652,53.4235954 L145.90969,53.4239128 C145.398506,53.4247871 144.881068,53.4271355 144.358353,53.4318501 C134.688118,53.5190716 126.8736,54.3831217 126.745149,50.2667605 C126.616697,46.1503993 134.351841,42.7427222 144.022076,42.6555007 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                    <path d="M131.5,72 C132.880712,72 134,73.1192881 134,74.5 C134,75.847036 132.934645,76.9452451 131.600549,76.9980149 L132.328516,85 L137.238855,85 L137.238855,91 L134.420855,91 L143.141692,138.862292 L139.238855,139.937708 L137.181855,129 L125.715091,129.001463 C124.626952,135.24891 123.814077,139.920021 123.810855,139.937708 L119.238855,138.862292 C119.251355,138.793681 119.262188,138.734218 119.272688,138.676585 L119.288355,138.590592 C119.351355,138.244792 119.426355,137.833126 119.801355,135.774792 C120.03573,134.488334 120.186399,133.661325 120.330236,132.871822 L120.387588,132.557021 C120.626557,131.24535 120.875016,129.881588 121.488855,126.512292 C122.381355,121.613459 122.970405,118.380229 123.521307,115.356389 L123.618183,114.824647 L123.666425,114.559851 L123.810855,113.767092 L123.944509,113.033478 L124.034229,112.541019 C124.897219,107.804162 125.869052,102.469876 127.958326,91.0020816 L125.238855,91 L125.238855,85 L130.328516,85 L131.059209,76.9612647 C129.88872,76.753048 129,75.7303098 129,74.5 C129,73.1192881 130.119288,72 131.5,72 Z M135.489855,120 L127.283368,120.000564 C126.86538,122.398563 126.451752,124.772467 126.063594,127.000716 L136.805855,127 L135.489855,120 Z M131.238855,97.3942853 C130.977129,98.8308698 129.316676,108.337877 127.631953,118.000956 L135.113855,118 L131.238855,97.3942853 Z M123.853553,66.368272 C124.048816,66.5635342 124.048816,66.8801167 123.853553,67.0753788 C119.753048,71.1758839 119.753048,77.8241161 123.853553,81.9246212 C124.048816,82.1198833 124.048816,82.4364658 123.853553,82.631728 C123.658291,82.8269901 123.341709,82.8269901 123.146447,82.631728 C118.655417,78.1406986 118.655417,70.8593014 123.146447,66.368272 C123.341709,66.1730099 123.658291,66.1730099 123.853553,66.368272 Z M139.853553,66.368272 C144.344583,70.8593014 144.344583,78.1406986 139.853553,82.631728 C139.658291,82.8269901 139.341709,82.8269901 139.146447,82.631728 C138.951184,82.4364658 138.951184,82.1198833 139.146447,81.9246212 C143.246952,77.8241161 143.246952,71.1758839 139.146447,67.0753788 C138.951184,66.8801167 138.951184,66.5635342 139.146447,66.368272 C139.341709,66.1730099 139.658291,66.1730099 139.853553,66.368272 Z M126.903806,69.1966991 C127.099068,69.3919613 127.099068,69.7085438 126.903806,69.9038059 C124.365398,72.4422138 124.365398,76.5577862 126.903806,79.0961941 C127.099068,79.2914562 127.099068,79.6080387 126.903806,79.8033009 C126.708544,79.998563 126.391961,79.998563 126.196699,79.8033009 C123.267767,76.8743687 123.267767,72.1256313 126.196699,69.1966991 C126.391961,69.001437 126.708544,69.001437 126.903806,69.1966991 Z M136.803301,69.1966991 C139.732233,72.1256313 139.732233,76.8743687 136.803301,79.8033009 C136.608039,79.998563 136.291456,79.998563 136.096194,79.8033009 C135.900932,79.6080387 135.900932,79.2914562 136.096194,79.0961941 C138.634602,76.5577862 138.634602,72.4422138 136.096194,69.9038059 C135.900932,69.7085438 135.900932,69.3919613 136.096194,69.1966991 C136.291456,69.001437 136.608039,69.001437 136.803301,69.1966991 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                    <g id="编组-2" transform="translate(56, 76)">
                        <path d="M8,0 L60,0 C62.209139,-4.05812251e-16 64,1.790861 64,4 L64,58 L64,58 L4,58 L4,4 C4,1.790861 5.790861,4.05812251e-16 8,0 Z" id="矩形" fill="url(#linearGradient-6)"></path>
                        <circle id="椭圆形" fill="url(#linearGradient-7)" cx="14" cy="42.8694411" r="3"></circle>
                        <circle id="椭圆形备份" fill="url(#linearGradient-7)" cx="22" cy="42.8694411" r="3"></circle>
                        <circle id="椭圆形备份-2" fill="url(#linearGradient-7)" cx="30" cy="42.8694411" r="3"></circle>
                        <circle id="椭圆形备份-3" fill="url(#linearGradient-7)" cx="38" cy="42.8694411" r="3"></circle>
                        <circle id="椭圆形备份-4" fill="url(#linearGradient-7)" cx="52" cy="42.8694411" r="5"></circle>
                        <rect id="矩形" fill="url(#linearGradient-8)" x="11" y="10" width="46" height="22" rx="2"></rect>
                        <path d="M2,10 L4,10 L4,10 L4,28 L2,28 C0.8954305,28 1.3527075e-16,27.1045695 0,26 L0,12 C-1.3527075e-16,10.8954305 0.8954305,10 2,10 Z" id="矩形" fill="url(#linearGradient-9)"></path>
                        <path d="M66,10 L68,10 L68,10 L68,28 L66,28 C64.8954305,28 64,27.1045695 64,26 L64,12 C64,10.8954305 64.8954305,10 66,10 Z" id="矩形备份" fill="url(#linearGradient-9)" transform="translate(66, 19) scale(-1, 1) translate(-66, -19)"></path>
                    </g>
                    <polygon id="Fill-70" fill="url(#linearGradient-10)" points="22.9090909 140.4 25.9090909 140.4 25.9090909 130.4 22.9090909 130.4"></polygon>
                    <path d="M69.947597,150 L59.3087782,150 C57.6759308,150 56.3522933,148.027853 56.3522933,145.595 L56.3522933,144.835868 C56.3522933,142.403111 57.6759308,140.430868 59.3087782,140.430868 L69.947597,140.430868 C71.5803154,140.430868 72.9040174,142.403111 72.9040174,144.835868 L72.9040174,145.595 C72.9040174,148.027853 71.5803154,150 69.947597,150" id="Fill-88" fill="#D6EEFF"></path>
                    <g id="路径" transform="translate(10, 119.8694)">
                        <polygon id="路径-9备份" fill="url(#linearGradient-11)" points="16.6137893 20.8271984 20.664534 5 32.8490131 20.1305589"></polygon>
                        <polygon id="路径-9备份-3" fill="url(#linearGradient-12)" points="134.613789 22.8271984 144.649656 6.60038344 150.849013 22.1305589"></polygon>
                        <mask id="mask-15" fill="white">
                            <use xlink:href="#path-14"></use>
                        </mask>
                        <use id="蒙版" fill="url(#linearGradient-13)" xlink:href="#path-14"></use>
                        <polygon id="矩形" fill="url(#linearGradient-16)" opacity="0.803578101" mask="url(#mask-15)" points="0.14964867 51.1166298 54.6281554 51.1166298 112 12.1305589 53.2424581 10.9673721"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>