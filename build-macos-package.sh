#!/bin/bash

set -e

TARGET_DIRECTORY=$1
SCRIPTS_DIRECTORY=$2
APPLE_PACKAGE_SIGNING_IDENTITY=$3
KEYCHAIN=$4

mkdir -p "${TARGET_DIRECTORY}/package"
mkdir -p "${TARGET_DIRECTORY}/product"
mkdir -p "${TARGET_DIRECTORY}/product-signed"

APP_ROOT="${TARGET_DIRECTORY}/release/bundle/macos/有人异地组网.app"

chmod -R 755 ${APP_ROOT}

pkgbuild \
    --analyze \
    --root ${APP_ROOT} \
    "${TARGET_DIRECTORY}/sdw.plist"
    
PACKAGE_PATH="${TARGET_DIRECTORY}/package/sdw.pkg"

pkgbuild \
    --identifier "usr.sdw" \
    --root ${APP_ROOT} \
    --component-plist ${TARGET_DIRECTORY}/sdw.plist \
    --install-location "/Applications/有人异地组网.app" \
    --scripts ${SCRIPTS_DIRECTORY} \
    "${PACKAGE_PATH}"

productbuild \
    --package "${PACKAGE_PATH}" \
    "${TARGET_DIRECTORY}/product/sdw.pkg"


# 注释掉签名步骤
# productsign \
#     --sign "${APPLE_PACKAGE_SIGNING_IDENTITY}" \
#     --keychain "${KEYCHAIN}" \
#     "${TARGET_DIRECTORY}/product/sdw.pkg" \
#     "${TARGET_DIRECTORY}/product-signed/sdw.pkg"

# 直接使用未签名的包作为最终输出
cp "${TARGET_DIRECTORY}/product/sdw.pkg" "${TARGET_DIRECTORY}/product-signed/sdw.pkg"
