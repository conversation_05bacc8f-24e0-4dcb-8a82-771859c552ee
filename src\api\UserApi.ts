import request from '../plugins/request'
import {requestIp} from './config.ts'

export const signInAPI = (data: any) => request.post(`${requestIp}/auth/client/signIn`, data)
export const getVersionAPI = (data: any) => request.get(`${requestIp}/version`, {
    params: data
})
export const logoutAPI = () => request.post(`${requestIp}/client/signOut`)
export const aliveAPI = () => request.get(`${requestIp}/client/`)
