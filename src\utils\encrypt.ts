import CryptoJS from 'crypto-js';

class AesCrypto {
    private static readonly DEFAULT_KEY = 'd6cefd9151bd04e24720ed70bebe2b86'; // 默认密钥，建议替换为你的实际密钥
    private static readonly DEFAULT_IV = 'ef25c3974f74077ab14698fbd89e46d9'; // 默认IV，建议替换为你的实际IV

    /**
     * AES加密
     * @param plainText 明文
     * @param key 密钥(可选)
     * @param iv 初始向量(可选)
     * @returns 加密后的十六进制字符串
     */
    public static encrypt(plainText: string, key?: string, iv?: string): string {
        const secretKey = CryptoJS.enc.Hex.parse(key  || this.DEFAULT_KEY);
        const ivVector = CryptoJS.enc.Hex.parse(iv  || this.DEFAULT_IV);

        const encrypted = CryptoJS.AES.encrypt(
            plainText,
            secretKey,
            {
                iv: ivVector,
            }
        );

        return encrypted.ciphertext.toString();
    }

    /**
     * AES解密
     * @param cipherText 密文(十六进制字符串)
     * @param key 密钥(可选)
     * @param iv 初始向量(可选)
     * @returns 解密后的UTF-8字符串
     */
    public static decrypt(cipherText: string, key?: string, iv?: string): string {
        const secretKey = CryptoJS.enc.Hex.parse(key  || this.DEFAULT_KEY);
        const ivVector = CryptoJS.enc.Hex.parse(iv  || this.DEFAULT_IV);

        const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: CryptoJS.enc.Hex.parse(cipherText)  } as any,
            secretKey,
            {
                iv: ivVector,
            }
        );

        return decrypted.toString(CryptoJS.enc.Utf8);
    }
}

export const aesEncrypt = (plainText: string): string => AesCrypto.encrypt(plainText);
export const aesDecrypt = (cipherText: string): string => AesCrypto.decrypt(cipherText);
