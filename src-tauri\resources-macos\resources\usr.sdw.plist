<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>usr.sdw</string>

    <!-- 环境变量配置 -->
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/sbin</string>
    </dict>

    <!-- 程序路径 -->
    <key>Program</key>
    <string>/usr/local/bin/sdw-service</string>

    <!-- 保持运行 -->
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
        <key>Crashed</key>
        <true/>
    </dict>

    <!-- 启动时运行 -->
    <key>RunAtLoad</key>
    <true/>

    <!-- 安全和权限配置 -->
    <key>UserName</key>
    <string>root</string>

    <!-- 标准输出和错误日志 -->
    <key>StandardOutPath</key>
    <string>/var/log/usr.sdw.out.log</string>
    <key>StandardErrorPath</key>
    <string>/var/log/usr.sdw.err.log</string>

    <!-- 进程限制 -->
    <key>SoftResourceLimits</key>
    <dict>
        <key>NumberOfFiles</key>
        <integer>1024</integer>
    </dict>

    <!-- 网络访问权限 -->
    <key>Sockets</key>
    <dict>
        <key>Listeners</key>
        <dict>
            <key>SockServiceName</key>
            <string>50051</string>
            <key>SockType</key>
            <string>stream</string>
            <key>SockFamily</key>
            <string>IPv4</string>
        </dict>
    </dict>

    <!-- 启动延迟 -->
    <key>StartInterval</key>
    <integer>10</integer>

    <!-- 退出超时 -->
    <key>ExitTimeOut</key>
    <integer>30</integer>

    <!-- 工作目录 -->
    <key>WorkingDirectory</key>
    <string>/usr/local/bin</string>
</dict>
</plist>
