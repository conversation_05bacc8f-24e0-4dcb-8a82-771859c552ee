<template>
  <div class="mainBox">
    <div ref="chartDom" class="chart" style="width: 100%;">
    </div>
    <div v-if="downloadList.length==0" class="maskAll">
      <img alt="" src="../../assets/images/chartSpace.svg">
      <div class="spaceText">暂无数据</div>
    </div>
  </div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {useMqttStore} from "../../store/index.js";
import * as echarts from 'echarts';

const mqttStore = useMqttStore()

const uploadList = ref([])
const maxChartUnit = ref('bps')
const downloadList = ref([])
const timeList = ref([])
/**
 *  监听mqttStore.chartData,动态获取x轴y轴数据
 */
watch(() => mqttStore.chartData, (newValue) => {
  getChartData(newValue)
}, {deep: true});
const getChartData = async (newValue) => {
  //  处理数据
  const {maxUnit, uploadValues, downloadValues} = processNetworkData(newValue);
  // 输出结果
  uploadList.value = uploadValues
  downloadList.value = downloadValues
  maxChartUnit.value = maxUnit
  // 调整数据
  timeList.value = newValue.map((item) => item.timeStr)
  await nextTick()
  setTimeout(() => {
    updateChart()
  }, 300)
}

/**
 *  获取y轴单位以及数据
 */
function processNetworkData(newValue) {
  // 1. 提取所有单位并找出最大单位
  const allUnits = [];

  newValue.forEach(item => {
    if (item.upStr) allUnits.push(parseUnit(item.upStr));
    if (item.downStr) allUnits.push(parseUnit(item.downStr));
  });

  const maxUnit = findMaxUnit(allUnits);

  // 2. 转换所有值为最大单位，并提取数值部分
  const processed = newValue.map(item => {
    const upValue = item.upStr ? convertToValue(item.upStr, maxUnit) : 0;
    const downValue = item.downStr ? convertToValue(item.downStr, maxUnit) : 0;

    // 保留原始对象结构（可选）
    return {
      ...item,
      upValue,  // 转换后的数值（纯数字）
      downValue, // 转换后的数值（纯数字）
      unit: maxUnit // 统一后的单位
    };
  });
  return {
    processedData: processed, // 完整处理后的数据（包含单位）
    maxUnit,                 // 统一后的单位
    uploadValues: processed.map(item => item.upValue),   // 纯数值数组
    downloadValues: processed.map(item => item.downValue) // 纯数值数组
  };
}

/**
 *  解析字符串为数值和单位
 */
function parseUnit(str) {
  const parts = str.split(' ');
  const value = parseFloat(parts[0]);
  const unit = parts[1].toLowerCase();
  return {value, unit};
}

/**
 *  找出最大单位
 */
function findMaxUnit(units) {
  const unitOrder = ['bps', 'kbps', 'mbps'];
  let maxIndex = -1;

  units.forEach(u => {
    const index = unitOrder.indexOf(u.unit);
    if (index > maxIndex) maxIndex = index;
  });

  return unitOrder[maxIndex];
}

/**
 *  将值转换为目标单位的数值
 */
function convertToValue(str, targetUnit) {
  const {value, unit} = parseUnit(str);

  if (unit === targetUnit.toLowerCase()) {
    return value; // 单位已匹配，直接返回数值
  }

  // 先转换为 bps
  let valueInBps;
  switch (unit) {
    case 'kbps':
      valueInBps = value * 1000;
      break;
    case 'mbps':
      valueInBps = value * 1000000;
      break;
    default:
      valueInBps = value; // bps
  }

  // 再从 bps 转换为目标单位
  switch (targetUnit.toLowerCase()) {
    case 'kbps':
      return valueInBps / 1000;
    case 'mbps':
      return valueInBps / 1000000;
    default:
      return valueInBps; // bps
  }
}

// 创建一个响应式引用来保存DOM元素
const chartDom = ref(null);
let chartInstance = ref(null);
/**
 *  生成配置项的函数
 */
const getOption = () => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {backgroundColor: '#6a7985'}
    }
  },
  grid: {
    top: '20',
    left: '20',
    right: '25',
    bottom: '20',
    containLabel: true,
    show: false
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: timeList.value,
      axisLine: {
        show: false // 隐藏x轴线
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      axisLabel: {
        formatter: `{value} ${maxChartUnit.value}`, // 显示单位（如：1.58 kbps）
      },
      splitLine: {  // 新增配置
        lineStyle: {
          type: 'dotted',  // 设置为虚线
          color: 'rgba(0,0,0,0.1)', // 可选调整颜色/透明度
          width: 1.5
        }
      }
    }
  ],
  series: [
    {
      name: '下载',
      type: 'line',
      stack: false,
      emphasis: {focus: 'series'},
      smooth: true,
      data: downloadList.value,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {offset: 0, color: 'rgba(60,120,255,0.6)'},   // 渐变顶部颜色
          {offset: 1, color: 'rgba(60,120,255,0)'}  // 渐变底部颜色（带透明度）
        ])
      },
    },
    {
      name: '上传',
      type: 'line',
      stack: false,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {offset: 0, color: 'rgba(0,209,63,0.6)'},   // 渐变顶部颜色
          {offset: 1, color: 'rgba(0,209,63,0)'}  // 渐变底部颜色（带透明度）
        ])
      },
      emphasis: {focus: 'series'},
      smooth: true,
      data: uploadList.value
    }
  ]
});
/**
 *  更新图表的函数
 */
const updateChart = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(getOption());
  }
};
/**
 *  初始化图表
 */
const initChart = async () => {
  await nextTick();
  chartInstance.value = echarts.init(chartDom.value);
  updateChart(); // 初始化时调用更新方法
};
/**
 *  初始化ECharts实例并设置配置项
 */
onMounted(async () => {
  await nextTick(); // 确保DOM已经渲染完成
  initChart()
  getChartData(mqttStore.chartData)
  window.addEventListener('resize', handleResize);
});
/**
 *  销毁ECharts实例
 */
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance != null && chartInstance.dispose) {
    chartInstance.dispose();
  }
});
/**
 *  动态调整图表尺寸
 */
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};
</script>

<style scoped>
/* 添加一些CSS样式来美化图表容器（可选） */
.mainBox {
  width: 100%;
  height: 328px;
  position: relative;
}

.chart {
  height: 328px;
}

.maskAll {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
}

.spaceText {
  width: 66px;
  height: 22px;
  font-family: PingFangHK-Regular;
  font-weight: 400;
  font-size: 13px;
  color: var(--usr-account-text);
  letter-spacing: 0;
  text-align: center;
  line-height: 22px;
  margin-top: 18px;
}
</style>
