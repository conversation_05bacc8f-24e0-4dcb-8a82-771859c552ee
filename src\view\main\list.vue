<script lang="ts" setup>
import {getAllNetworkAPI, getNetworkAndPeerAPI, getNetworkClientAPI} from "../../api/network.ts";
import {onMounted, reactive, ref, watch} from "vue";
import {invoke} from "@tauri-apps/api/tauri";
import {useMqttStore} from "../../store";
import filterBtn from '../../components/btn/filterBtn.vue'
// 定义一个响应式的数组 mainList，用于存储从网络请求中获取的数据
const mainList: any = ref([])
const peerInfo: any = ref(null)
const activeSN = ref('')
const mqttStore = useMqttStore()
const timer: any = ref(null)
/**
 * 自动刷新 - 只更新peerStatus并追加新数据
 * 此函数每10秒执行一次，更新在线状态并追加新数据
 */
const autoUpdateAndAppend = async () => {
  try {
    // 获取最新的完整列表数据
    let res: any = await getAllNetworkAPI(filter)

    if (res.msg === 'success' && Array.isArray(res.result)) {
      const newList = res.result

      // 1. 更新现有项的peerStatus
      mainList.value.forEach((item: any) => {
        const foundItem = newList.find((newItem: any) => newItem.pId === item.pId)
        if (foundItem) {
          // 只更新peerStatus状态
          item.peerStatus = foundItem.peerStatus
        }
      })

      // 2. 追加mainList中不存在的新数据
      newList.forEach((newItem: any) => {
        const existsInMainList = mainList.value.some((item: any) => item.pId === newItem.pId)
        if (!existsInMainList) {
          // 添加默认lineType
          newItem.lineType = 'psp'
          console.log('添加新数据:', newItem.peerName || newItem.pId)
          mainList.value.push(newItem)
        }
      })
    }

    // 3. 获取线路状态
    // let result: any = await invoke('get_edges', {})
    // if (Array.isArray(result.data)) {
    //   mainList.value.forEach((item: any) => {
    //     const selItem = result.data.find((info: any) => info.mac_address == item.mac)
    //     if (selItem) {
    //       item.lineType = selItem.operation_mode
    //     } else {
    //       item.lineType = 'psp'
    //     }
    //   })
    // }
  } catch (error) {
    console.error('自动刷新失败:', error)
  }
}
/**
 * 开启定时刷新
 */
const getP2PList = () => {
  console.log(mqttStore.wgConfig?.level, '组网定时器')
  clearInterval(timer.value)
  timer.value = null
  updateMainList()
  timer.value = setInterval(() => {
    autoUpdateAndAppend()
  }, 10000)
}
/**
 * 更新mainList
 */
const updateMainList = async () => {
  console.log(mqttStore.wgConfig?.level, '组网更新')
  if (mqttStore.wgConfig?.level == 2) {

  } else {
    clearInterval(timer.value)
    timer.value = null
    return
  }
  // let result: any = await invoke('get_edges', {})
  // // 判断 result 的data  是数组类型，进行处理否则不处理
  // if (Array.isArray(result.data)) {
  //   console.log(result, mainList.value)
  //   // 遍历mainList.value， 判断每一项的mac是否在result。data数组中的mac_address  有一样的值就把这一项operation_mode赋值给当前的lineType
  //   mainList.value.forEach((item: any) => {
  //     const selItem = result.data.find((info: any) => info.mac_address == item.mac)
  //     if (selItem) {
  //       item.lineType = selItem.operation_mode
  //     } else {
  //       item.lineType = 'psp'
  //     }
  //   })
  // }
}
/**
 * 异步获取主列表数据
 * 此函数通过调用 getNetworkList 方法从网络获取数据，并更新 mainList 数组
 */
const getMainList = async () => {
  let resPeer: any = await getNetworkAndPeerAPI()
  console.log(resPeer)
  if (resPeer.msg == 'success') {
    peerInfo.value = resPeer.result
  }
  resetList()
}
/**
 * 获取网关客户端，设置默认选中，调整中心节点位置
 */
const resetList = async () => {
  console.log('getMainList', filter)
  let res: any = await getAllNetworkAPI(filter)
  if (res.msg === 'success') {
    let arr = res.result
    let list = arr.sort((a: any, b: any) => {
      if (a.peerStatus && !b.peerStatus) {
        return -1;
      } else if (!a.peerStatus && b.peerStatus) {
        return 1;
      } else {
        return 0;
      }
    });
    console.log(list)
    mainList.value = list.sort((a: any, b: any) => {
      if (a.isCenter && !b.isCenter) {
        return -1;
      } else if (!a.isCenter && b.isCenter) {
        return 1;
      } else {
        return 0;
      }
    });
    console.log(mainList.value,'排序')
    // 数组每个选项增加lineType  默认值是psp
    mainList.value.forEach((item: any) => {
      item.lineType = 'psp'
    })
    showIndex.value++
    if (res.result.length > 0) {
      activeSN.value = res.result[0].pId
      activePeer.value = res.result[0]
    } else {
      activeSN.value = ''
      activePeer.value = {
        peerType:2
      }
    }
    drawer.value = false
    getP2PList()
  }
}
/**
 * 格式化IP
 */
const formatIP = (ip: any) => {
  const ipv4 = ip.split('/')
  if (ipv4.length == 2) {
    return ipv4[0]
  } else {
    return ip
  }
}
/**
 * 切换网络，客户端网关终端切换
 */
const changeNetWork = (item: any) => {
  activeSN.value = item.pId
  activePeer.value = item
};
const showIndex = ref(0)
onMounted(() => {
  showIndex.value = 0
  getMainList()
})
const tableData = ref([])
/**
 * 调试打开链接
 */
const handleClick = async (item: any) => {
  await invoke('open_browser', {url: item.debugUrl})
  const sendConfig = {
    "cmd": "debugLog",
    "data": {
      "sn": activeSN.value,
      "debugType": item.debugType,
      "endPointName": item.customName || item.endPointName
    }
  }
  mqttStore.publishCmd(JSON.stringify(sendConfig))
}
const drawer = ref(false)
const cancelClick = () => {
  drawer.value = false
}
/**
 * 确认筛选
 */
const confirmClick = () => {
  resetList()
}
/**
 * 重置筛选
 */
const resetFilter = () => {
  filter.q = ''
  filter.peerStatus = -1
  filter.peerType = -1
  resetList()
}
/**
 * 定义筛选
 */
const filter = reactive({
  q: '',
  peerStatus: -1,
  peerType: -1,
})
const activePeer: any = ref({})
/**
 * 监听网关客户端变化获取终端列表
 */
watch(activePeer, async (newValue) => {
  console.log('activePeer', newValue, newValue.peerType)
  if (newValue.peerType == 0) {
    let res: any = await getNetworkClientAPI({
      sn: newValue.pId,
      page: 1,
      size: 1000
    })
    if (res.msg === 'success') {
      // res.result排序isCenter 为true的在前
      tableData.value = res.result.rows
    }
  }
}, {
  immediate: true
})
</script>

<template>
  <div class="mainBox">
    <div class="leftBox">
      <div class="card-header">
        <div class="card-line"></div>
        <div class="card-title">成员列表</div>
        <img alt=""  src="../../assets/images/filter.svg"
             @click="drawer = !drawer"/>
      </div>
      <el-input v-model="filter.q"  placeholder="请输入" size="small" @blur="confirmClick"
                @keyup.enter.native="confirmClick">
        <template #prefix>
          <img alt="" class="search" src="../../assets/images/search.svg"/>
        </template>
      </el-input>
      <el-drawer v-model="drawer"
                 :show-close="false"
                 direction="rtl">
        <template #default>
          <div class="drawerTitle">成员类型</div>
          <div>
            <el-radio v-model="filter.peerType" :value="-1" size="large">
              全部
            </el-radio>
            <el-radio v-model="filter.peerType" :value="0" size="large">
              网关
            </el-radio>
            <el-radio v-model="filter.peerType" :value="1" size="large">
              客户端
            </el-radio>
          </div>
          <div class="line"></div>
          <div class="drawerTitle">在线状态</div>
          <div>
            <el-radio v-model="filter.peerStatus" :value="-1" size="large">
              全部
            </el-radio>
            <el-radio v-model="filter.peerStatus" :value="0" size="large">
              未入网
            </el-radio>
            <el-radio v-model="filter.peerStatus" :value="1" size="large">
              在网
            </el-radio>
          </div>
        </template>
        <template #footer>
          <div style="display: flex;justify-content: flex-end">
            <filterBtn type="border" :length="60" text="取消" @click="cancelClick"></filterBtn>
            <div style="width: 12px"></div>
            <filterBtn  :length="88" text="立即筛选"  @click="confirmClick"></filterBtn>
          </div>
        </template>
      </el-drawer>
      <ul class="mainList">
        <div v-if="mainList.length==0&&showIndex>0" class="spaceNetwork">
          <div class="spaceText">暂无成员</div>
          <baseBtn text="重置筛选" :length="75" :height="29"   @click="resetFilter"></baseBtn>
        </div>
        <li v-for="(item, index) in mainList" :key="index" :class="[activeSN==item.pId?'mainItem active':'mainItem']"
            @click="changeNetWork(item)">
          <div style="display: flex;align-items: center;cursor: pointer">
            <template v-if="item.peerStatus==1">
              <img v-if="item.peerType==0" alt="" class="clientImg" src="../../assets/images/routeActive.svg">
              <img v-else alt="" class="clientImg" src="../../assets/images/clientActive.svg">
            </template>
            <template v-else>
              <img v-if="item.peerType==0" alt="" class="clientImg" src="../../assets/images/route.svg">
              <img v-else alt="" class="clientImg" src="../../assets/images/client.svg">
            </template>
            <div>
              <div style="display: flex;justify-content: flex-start;align-items: center;flex: 1">
                <div class="name">{{ item.peerName || item.pId }}</div>
                <img v-if="item.isCenter" alt="" class="type" src="../../assets/images/center.svg">
                <img v-if="item.lineType=='p2p'" alt="" class="type" src="../../assets/images/p2p.svg">
                <img v-else alt="" class="type" src="../../assets/images/open.svg">
              </div>
              <div v-if="peerInfo.wan.level==3" class="ip">
                {{ formatIP(item.wgAddress || '_') }}
                <copyBtn v-if="item.wgAddress" :ip="item.wgAddress"></copyBtn>
              </div>
              <div v-if="peerInfo.wan.level==2" class="ip">
                {{ formatIP( item.secondIp || '_') }}
                <copyBtn v-if="item.secondIp" :ip="item.secondIp"></copyBtn>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <template v-if="activePeer.peerType==0">
      <div v-if="tableData.length!=0" class="rightBox">
        <div class="card-header ma-16">
          <div class="card-line"></div>
          <div class="card-title">终端列表</div>
        </div>
        <el-table :data="tableData" border stripe empty-text="网关下没有终端">
          <el-table-column label="序号" width="60">
            <template #default="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column label="名称" prop="customName" width="146">
            <template #default="scope">
              <img v-if="scope.row.isStatic"  alt="" class="type" src="../../assets/images/top.svg">
              {{ scope.row.customName || scope.row.endPointName }}
            </template>
          </el-table-column>
          <el-table-column label="在网状态" width="102">
            <template #default="scope">
              <div class="green" v-if="scope.row.online"><div class="greenDot"></div>在网</div>
              <div class="red" v-else><div class="redDot"></div>未入网</div>
            </template>
          </el-table-column>
          <el-table-column label="组网IP" prop="virtualIp" width="138"/>
          <el-table-column label="真实IP" prop="lanIP" width="138">
          </el-table-column>
          <el-table-column label="MAC地址" prop="mac" width="180">
            <template #default="scope">
              <div v-if="scope.row.mac">{{ scope.row.mac }}</div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column label="接入类型" width="180">
            <template #default="scope">
              <div v-if="scope.row.conType == 0">无线</div>
              <div v-else-if="scope.row.conType == 1">有线</div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100">
            <template #default="scope">
              <div class="blueLink" v-if="scope.row.debugEnable" @click="handleClick(scope.row)">
                调试
              </div>
<!--              <div v-else class="whiteLink">-->
<!--                调试-->
<!--              </div>-->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="rightSpace">
        <img alt="" class="type" src="../../assets/images/space.svg"/>
        <div class="spaceText">无任何终端</div>
      </div>
    </template>
    <div v-else-if="activePeer.peerType==1"  class="rightSpace">
      <img alt="" class="type" src="../../assets/images/spaceCenter.svg"/>
      <div class="spaceText">当前节点为客户端</div>
    </div>
    <div v-else class="rightSpace">
      <img alt="" class="type" src="../../assets/images/space.svg"/>
      <div class="spaceText">无任何终端</div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.mainBox {
  height: calc(100vh - 56px);
  display: flex;
  margin-top: 24px;
  margin-right: 16px;
  margin-bottom: 32rpx;
  overflow: hidden;
  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none;
  }
  .card-header {
    height: 24px;
    font-family: PingFangHK-Medium;
    font-weight: 500;
    font-size: 16px;
    color: var(--usr-btn-border-text);
    letter-spacing: 0;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    .card-line {
      width: 2px;
      height: 14px;
      background-image: linear-gradient(179deg, #3C78FF 0%, #0F39A8 100%);
      border-radius: 1px;
      margin-right: 8px;
    }

    .card-title {
      flex: 1;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 16px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      line-height: 24px;
    }

    img {
      width: 24px;
      height: 24px;
    }
  }
  .ma-16{
    margin: 16px;
  }
  .leftBox {
    width: 152px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    background: var(--usr-banner-bg);
    box-shadow: 0 2px 4px 0 var(--usr-banner-shadow);
    padding: 16px;
    border-radius: 6px;

    .mainList {
      flex: 1;
      margin: 8px 0 0;
      overflow-y: scroll;
      padding-bottom: 30px;

      .spaceText {
        width: 100%;
        height: 22px;
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 14px;
        color: var(--usr-account-text);
        letter-spacing: 0;
        line-height: 22px;
        margin-bottom: 5px;
        text-align: center;
      }

      .mainItem {
        padding: 8px;
        border-radius: 4px;
        color: var(--usr-title);
        font-family: "PingFang HK";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        white-space: normal; /* 允许文字换行 */
        word-break: break-all;

        .name {
          height: 20px;
          font-family: PingFangHK-Regular;
          font-weight: 400;
          font-size: 13px;
          color: var(--usr-btn-border-text);
          letter-spacing: 0;
          line-height: 20px;
          // 单行省略
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 2px;
        }

        .type {
          height: 12px;
        }

        .clientImg {
          width: 20px;
          height: 32px;
          margin-right: 4px;
          flex-shrink: 0;
        }


        .ip {
          height: 10px;
          font-family: PingFangHK-Regular;
          font-weight: 400;
          font-size: 10px;
          color: var(--usr-account-text);
          letter-spacing: 0;
          line-height: 10px;
          display: flex;
          align-items: center;
        }
      }

      .active {
        background: var(--usr-menu-hover);
      }
    }
  }

  .rightBox {
    flex: 1;
    height: 100%;
    overflow-y: scroll;
    margin-left: 8px;
    background: var(--usr-banner-bg);
    border-radius: 6px;
    .red{
      height: 18px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #F82765;
      line-height: 18px;
      display: flex;
      align-items: center;
    }
    .redDot{
      width: 8px;
      height: 8px;
      background: #F82765;
      border-radius: 5px;
      margin-right: 4px;
    }

    .green{
      height: 18px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #00D13F;
      line-height: 18px;
      display: flex;
      align-items: center;
    }
    .greenDot{
      content:'';
      width: 8px;
      height: 8px;
      background: #00D13F;
      border-radius: 5px;
      margin-right: 4px;
    }
  }
}


ul {
  padding: 0;
}

li {
  font-size: 12px;
  list-style: none;
}

.rightBox {
  flex: 1;
}

.rightSpace {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--usr-banner-bg);
  margin-left: 8px;
  border-radius: 6px;

  .type {
    width: 180px;
    height: 180px;
  }

  .spaceText {
    width: 100%;
    height: 22px;
    font-family: PingFangHK-Regular;
    font-weight: 400;
    font-size: 13px;
    color: var(--usr-account-text);
    letter-spacing: 0;
    text-align: center;
    line-height: 22px;
  }
}


.mitBox span {
  margin-right: 10px;
}


.rightTitle {
  display: flex;
  padding: 16px 12px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  color: var(--usr-title);

  /* medium/medium */
  font-family: "PingFang HK";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 150% */
}


.line{
  border-bottom: 1px solid  var(--usr-input-border) ;
  width: 100%;
  height: 0;
  margin: 16px 0;
}
.drawerTitle {
  width: 100%;
  height: 22px;
  font-family: PingFangHK-Regular;
  font-weight: 400;
  font-size: 14px;
  color:var(--usr-btn-border-text);
  letter-spacing: 0;
  line-height: 22px;
}

.spaceNetwork {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}


// 框架修改
:deep(.el-drawer) {
  background: var(--usr-white);
}

:deep(.el-input) {
  width: 150px !important;
}

:deep(.el-input__inner) {
  border-radius: 0;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  color: var(--usr-input-text)!important;
}
/* 修改输入框placeholder的文字颜色 */
:deep(.el-input__inner::placeholder) {
  color: var(--usr-input-placeholder)!important;
}
.blueLink{
  cursor: pointer;
  height: 20px;
  font-family: PingFangHK-Medium;
  font-weight: 500;
  font-size: 12px;
  color: var(--usr-account-change);
  letter-spacing: 0;
  line-height: 20px;
}
.whiteLink{
  height: 20px;
  font-family: PingFangHK-Medium;
  font-weight: 500;
  font-size: 12px;
  color: var(--usr-btn-disabled);
  letter-spacing: 0;
  line-height: 20px;
}

:deep(.el-table) {
  background: var(--usr-white);
  max-height: 98%;

  .el-table__cell {
    height: 22px;
    font-family: PingFangHK-Regular;
    font-weight: 400;
    font-size: 13px;
    color: #25345C;
    letter-spacing: 0;
    line-height: 22px;
  }
  thead{
    .cell{
      height: 22px;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 14px;
      color: var(--usr-account-text);
      letter-spacing: 0;
      text-align: left;
      line-height: 22px;
    }
  }
}
:deep(.el-radio) {
  color: var(--usr-account-text);
}
/* 修改输入框placeholder的文字颜色 */
:deep(.el-radio__input.is-checked+.el-radio__label) {
  color: var(--user-radio)!important;
}
:deep(.el-radio__input.is-checked .el-radio__inner) {
  background: var(--user-radio)!important;
  border-color: var(--user-radio)!important;
}
:deep(.el-drawer__header){
  margin-bottom: 0;
  margin-top: 4px;
}
:deep(.is-focus) {
  box-shadow: 0 0 0 1px var(--usr-input-border)!important;
}
</style>
