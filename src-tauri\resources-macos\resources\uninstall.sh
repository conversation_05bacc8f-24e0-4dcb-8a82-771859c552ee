#!/bin/bash

# macOS LaunchDaemon 卸载脚本
# 需要管理员权限运行

set -e  # 遇到错误立即退出

# 配置变量
SERVICE_BINARY=sdw-service
DAEMON_PROPERTY_FILE=usr.sdw.plist
WIREGUARD_GO_BINARY=wireguard-go
DAEMON_NAME=usr.sdw
PACKAGE_ID=usr.sdw
APP_BUNDLE=有人异地组网.app
DAEMON_PLIST_PATH=/Library/LaunchDaemons/${DAEMON_PROPERTY_FILE}
LOG_DIR=/var/log

# 日志函数
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_warn() {
    echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查是否以root权限运行
check_root_privileges() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要管理员权限运行"
        log_error "请使用: sudo $0"
        exit 1
    fi
}

# 停止并卸载LaunchDaemon
remove_launch_daemon() {
    log_info "停止并卸载LaunchDaemon..."

    # 检查服务是否正在运行
    if launchctl list | grep -q "$DAEMON_NAME"; then
        log_info "停止服务: $DAEMON_NAME"
        if ! launchctl stop "$DAEMON_NAME" 2>/dev/null; then
            log_warn "停止服务时出现警告，继续卸载过程"
        fi

        # 等待服务完全停止
        sleep 2
    else
        log_info "服务未运行: $DAEMON_NAME"
    fi

    # 卸载LaunchDaemon
    if [[ -f "$DAEMON_PLIST_PATH" ]]; then
        log_info "卸载LaunchDaemon: $DAEMON_PLIST_PATH"
        if ! launchctl unload "$DAEMON_PLIST_PATH" 2>/dev/null; then
            log_warn "卸载LaunchDaemon时出现警告，继续删除文件"
        fi

        # 删除plist文件
        rm -f "$DAEMON_PLIST_PATH"
        log_info "删除plist文件: $DAEMON_PLIST_PATH"
    else
        log_info "LaunchDaemon plist文件不存在: $DAEMON_PLIST_PATH"
    fi
}

# 删除二进制文件
remove_binaries() {
    log_info "删除二进制文件..."

    # 删除WireGuard二进制文件
    if [[ -f "/usr/local/bin/${WIREGUARD_GO_BINARY}" ]]; then
        rm -f "/usr/local/bin/${WIREGUARD_GO_BINARY}"
        log_info "删除WireGuard二进制文件: /usr/local/bin/${WIREGUARD_GO_BINARY}"
    else
        log_info "WireGuard二进制文件不存在: /usr/local/bin/${WIREGUARD_GO_BINARY}"
    fi

    # 删除服务二进制文件
    if [[ -f "/usr/local/bin/${SERVICE_BINARY}" ]]; then
        rm -f "/usr/local/bin/${SERVICE_BINARY}"
        log_info "删除服务二进制文件: /usr/local/bin/${SERVICE_BINARY}"
    else
        log_info "服务二进制文件不存在: /usr/local/bin/${SERVICE_BINARY}"
    fi
}

# 清理日志文件
cleanup_logs() {
    log_info "清理日志文件..."

    # 删除日志文件
    if [[ -f "$LOG_DIR/usr.sdw.out.log" ]]; then
        rm -f "$LOG_DIR/usr.sdw.out.log"
        log_info "删除输出日志: $LOG_DIR/usr.sdw.out.log"
    fi

    if [[ -f "$LOG_DIR/usr.sdw.err.log" ]]; then
        rm -f "$LOG_DIR/usr.sdw.err.log"
        log_info "删除错误日志: $LOG_DIR/usr.sdw.err.log"
    fi
}

# 删除应用程序
remove_application() {
    log_info "删除应用程序..."

    local app_path="/Applications/${APP_BUNDLE}"
    if [[ -d "$app_path" ]]; then
        rm -rf "$app_path"
        log_info "删除应用程序: $app_path"
    else
        log_info "应用程序不存在: $app_path"
    fi
}

# 清理包管理器记录
cleanup_package_records() {
    log_info "清理包管理器记录..."

    if pkgutil --pkgs | grep -q "$PACKAGE_ID"; then
        if pkgutil --forget "$PACKAGE_ID" > /dev/null 2>&1; then
            log_info "清理包记录: $PACKAGE_ID"
        else
            log_warn "清理包记录时出现警告: $PACKAGE_ID"
        fi
    else
        log_info "包记录不存在: $PACKAGE_ID"
    fi
}

# 验证卸载结果
verify_uninstall() {
    log_info "验证卸载结果..."

    local issues=0

    # 检查LaunchDaemon是否还在运行
    if launchctl list | grep -q "$DAEMON_NAME"; then
        log_error "LaunchDaemon仍在运行: $DAEMON_NAME"
        ((issues++))
    fi

    # 检查plist文件是否还存在
    if [[ -f "$DAEMON_PLIST_PATH" ]]; then
        log_error "plist文件仍存在: $DAEMON_PLIST_PATH"
        ((issues++))
    fi

    # 检查二进制文件是否还存在
    if [[ -f "/usr/local/bin/${SERVICE_BINARY}" ]] || [[ -f "/usr/local/bin/${WIREGUARD_GO_BINARY}" ]]; then
        log_error "二进制文件仍存在于 /usr/local/bin/"
        ((issues++))
    fi

    # 检查应用程序是否还存在
    if [[ -d "/Applications/${APP_BUNDLE}" ]]; then
        log_error "应用程序仍存在: /Applications/${APP_BUNDLE}"
        ((issues++))
    fi

    if [[ $issues -eq 0 ]]; then
        log_info "卸载验证通过，所有组件已成功删除"
    else
        log_error "卸载验证失败，发现 $issues 个问题"
        return 1
    fi
}

# 主卸载流程
main() {
    log_info "=== macOS LaunchDaemon 卸载开始 ==="

    # 检查权限
    check_root_privileges

    # 停止并卸载LaunchDaemon
    remove_launch_daemon

    # 删除二进制文件
    remove_binaries

    # 清理日志文件
    cleanup_logs

    # 删除应用程序
    remove_application

    # 清理包管理器记录
    cleanup_package_records

    # 验证卸载结果
    if verify_uninstall; then
        log_info "=== 卸载完成 ==="
        log_info "所有组件已成功删除"
    else
        log_error "=== 卸载完成但存在问题 ==="
        log_error "请手动检查并清理剩余文件"
        exit 1
    fi
}

# 执行主函数
main "$@"
