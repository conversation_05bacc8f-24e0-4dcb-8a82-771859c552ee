<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="12px" viewBox="0 0 20 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>p2p</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="20" height="12" rx="1.5"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20" height="12" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="PC客户端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="成员-有成员" transform="translate(-174, -276)">
            <g id="Frame-8" transform="translate(72, 24)">
                <g id="Frame-148" transform="translate(16, 86)">
                    <g id="Frame-145" transform="translate(0, 156)">
                        <g id="Frame-157" transform="translate(30, 6)">
                            <g id="p2p" transform="translate(56, 4)">
                                <use id="矩形" stroke="#3C78FF" mask="url(#mask-2)" stroke-width="1.2" stroke-dasharray="0,0" xlink:href="#path-1"></use>
                                <text id="P2P" font-family="PingFangHK-Regular, PingFang HK" font-size="7" font-weight="normal" line-spacing="8" fill="#3C78FF">
                                    <tspan x="3" y="8.5">P2P</tspan>
                                </text>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>