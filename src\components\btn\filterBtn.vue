<script setup lang="ts">
const props = defineProps({
  text: String,
  type: String,
  length: Number,
})
</script>

<template>
  <div :class="['my-border-button',  'w-'+props.length]" v-if="props.type=='border'">{{props.text}}</div>
  <div :class="['my-button',  'w-'+props.length]"  v-else>{{props.text}}</div>
</template>

<style scoped>
.my-button {
  /* 默认状态 */
  height: 32px;
  line-height: 32px;
  background:var(--usr-primary)!important;
  border-radius: 4px;
  cursor: pointer;
  font-family: PingFangHK-Medium;
  font-weight: 500;
  font-size: 14px;
  color: var(--usr-white)!important;
  letter-spacing: 0;
  text-align: center;
}

/* 悬浮状态 */
.my-button:hover {
  background: var(--usr-btn-hover)!important;
}

/* 点击状态 */
.my-button:active {
  background: var(--usr-btn-active)!important;
}
.w-88{
  width: 88px;
}
.w-60{
  width: 60px;
}
.my-border-button {
  height: 32px;
  line-height: 32px;
  border: 1px solid var(--usr-input-border)!important;
  border-radius: 4px;
  font-family: PingFangHK-Medium;
  font-weight: 500;
  font-size: 14px;
  color: var(--usr-btn-border-text)!important;
  letter-spacing: 0;
  text-align: center;
}

/* 悬浮状态 */
.my-border-button:hover {
  border: 1px solid  var(--usr-btn-hover)!important;
  color:  var(--usr-btn-hover)!important;
}

/* 点击状态 */
.my-border-button:active {
  border: 1px solid  var(--usr-btn-active)!important;
  color:  var(--usr-btn-active)!important;
}
</style>
