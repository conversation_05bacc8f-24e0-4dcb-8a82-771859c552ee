<script lang="ts" setup>
import {onMounted, reactive, ref} from "vue";
import {getAccountInfoAPI, modifyNameAPI, modifyPasswordAPI} from "../../api/network.ts";
import type {FormInstance, FormRules} from "element-plus";
import {useRouter} from "vue-router";
import {useMqttStore, useTokenStore} from '../../store'
import {invoke} from "@tauri-apps/api/tauri";
import {logoutAPI} from "../../api/UserApi.ts";

const mqttStore = useMqttStore()
const tokenStore = useTokenStore()
const router = useRouter()
const userInfo: any = ref({
  name: '',
  password: ''
})
onMounted(() => {
  getAccountInfo()
})
/**
 *  获取账户信息
 */
const getAccountInfo = async () => {
  let res: any = await getAccountInfoAPI()
  if (res.msg === 'success') {
    userInfo.value = res.result
    userInfo.value.password = '******'
  }
}
const showPassword = ref(false)
const showName = ref(false)
const passwordForm = ref({
  password: '',
  newPassword: '',
  confirmPassword: ''
})
const nameForm = ref({
  name: ''
})
/**
 *  重置表单
 */
const resetForm = () => {
  passwordForm.value = {
    password: '',
    newPassword: '',
    confirmPassword: ''
  }
  nameForm.value = {
    name: ''
  }
}
/**
 * 显示弹窗
 */
const showModify = (type: number) => {
  resetForm()
  if (type == 0) {
    showName.value = !showName.value
  } else if (type == 1) {
    showPassword.value = !showPassword.value
  }
}
/**
 * 修改密码，关闭弹窗
 */
const modifyPassword = async () => {
  let res: any = await modifyPasswordAPI({
    oldPassword: passwordForm.value.password,
    password: passwordForm.value.newPassword
  })
  if (res.msg === 'success') {
    showPassword.value = false
    getAccountInfo()
    let list: any = localStorage.getItem('userList')
    list = JSON.parse(list)
    for (let i in list) {
      if (list[i].currentAccount == true) {
        list[i].password = passwordForm.value.newPassword
      }
    }
    localStorage.setItem('userList', JSON.stringify(list))
    resetForm()
    mqttStore.disconnect()
    let resLogOut: any = await logoutAPI()
    if (resLogOut.msg === 'success') {
      tokenStore.exitToken()
      router.push({path: '/login'})
    }
  }
}
/**
 * 修改用户名，关闭弹窗
 */
const modifyName = async () => {
  let res: any = await modifyNameAPI({
    name: nameForm.value.name + ''
  })
  if (res.msg === 'success') {
    showName.value = false
    getAccountInfo()
    resetForm()
  }
}
/**
 * 用户名规则
 */
const checkName = (_rule: any, value: any, callback: any) => {
  // 校验值必须为1-30字符
  if (value.length < 1 || value.length > 30) {
    return callback(new Error('请输入30字符以内的姓名'))
  } else {
    callback()
  }
}
/**
 * 密码规则
 */
const validatePassword = (_rule: any, value: any, callback: any) => {
  // 校验密码必须6-30位
  if (value.length < 6 || value.length > 30) {
    return callback(new Error('请输入6-30位密码'))
  } else {
    callback()
  }
}
/**
 * 密码规则，确认密码
 */
const validateRePassword = (_rule: any, value: any, callback: any) => {
  if (value.length < 6 || value.length > 30) {
    return callback(new Error('请再次输入6-30位密码'))
  } else if (value !== passwordForm.value.newPassword) {
    callback(new Error("两次密码输入不一致!"))
  } else {
    callback()
  }
}
/**
 * 用户名表单规则
 */
const rules = reactive<FormRules<typeof nameForm>>({
  name: [{validator: checkName, trigger: 'blur'}],
})
/**
 * 密码表单规则
 */
const rulesPassword = reactive<FormRules<typeof passwordForm>>({
  password: [{validator: validatePassword, trigger: 'blur'}],
  newPassword: [{validator: validatePassword, trigger: 'blur'}],
  confirmPassword: [{validator: validateRePassword, trigger: 'blur'}],
})
/**
 * 提交表单
 */
const ruleFormRef = ref<FormInstance>()
const submitPasswordForm = (ruleFormRef: FormInstance | undefined) => {
  if (!ruleFormRef) return
  ruleFormRef.validate((valid) => {
    if (valid) {
      modifyPassword()
    } else {
      console.log('error submit!')
    }
  })
}
/**
 * 提交表单
 */
const ruleNameFormRef = ref<FormInstance>()
const submitNameForm = (ruleNameFormRef: FormInstance | undefined) => {
  if (!ruleNameFormRef) return
  ruleNameFormRef.validate((valid) => {
    if (valid) {
      modifyName()
    } else {
      console.log('error submit!')
    }
  })
}
/**
 * 调试打开链接
 */
const handleClick = async (item: any) => {
  await invoke('open_browser', {url: item.url})
}
</script>
<template>
  <div class="mainBox">
    <div class="card mt-24">
      <div class="card-header">
        <div class="card-line"></div>
        <div class="card-title">{{ '个人信息' }}</div>
      </div>
      <div class="card-grid">
        <div class="infoItem">
          <div class="infoLabel">姓名：</div>
          <div class="infoContent"
               @click="showModify(0)">
            <el-input v-model="userInfo.name" placeholder="请设置姓名" readonly style="width: 240px">
              <template #suffix>
                <img alt="" src="../../assets/images/edit_line.svg">
              </template>
            </el-input>
          </div>
        </div>
        <div class="infoItem">
          <div class="infoLabel">密码：</div>
          <div class="infoContent"
               @click="showModify(1)">
            <el-input v-model="userInfo.password" placeholder="请设置密码" readonly style="width: 240px">
              <template #suffix>
                <img alt="" src="../../assets/images/edit_line.svg">
              </template>
            </el-input>
          </div>
        </div>
      </div>
    </div>
    <div class="bannerBox">
      <div class="bannerItem">
        <div class="img-container">
          <img alt="" class="imgTop" src="../../assets/images/googSwitch.png">
        </div>
        <div class="contentBox">
          <div class="desc">网络基础设施 - 产品推荐</div>
          <div class="title">工业级交换机 - 白金系列</div>
          <div class="text">-40~85℃丨6kV防雷丨铝合金外壳丨导轨安装</div>
          <button class="btn" @click="handleClick({url: 'https://www.usr.cn/Product/372.html'})">了解更多
            <img alt="" class="icon" src="../../assets/images/arrow_right_line.svg"></button>
        </div>
      </div>
      <div class="bannerItem">
        <div class="img-container">
          <img class="imgTop" src="../../assets/images/5G.png">
        </div>
        <div class="contentBox">
          <div class="desc">网络基础设施 - 产品推荐</div>
          <div class="title">5G+WiFi6 工业路由器</div>
          <div class="text">RS485+232丨边缘计算丨JSON上报丨MQTT</div>
          <button class="btn" @click="handleClick({url: 'https://www.usr.cn/Product/348.html'})">了解更多
            <img alt="" class="icon" src="../../assets/images/arrow_right_line.svg"></button>
        </div>
      </div>
      <div class="bannerItem">
        <div class="img-container">
          <img class="imgTop" src="../../assets/images/service.png">
        </div>
        <div class="contentBox">
          <div class="desc">网络基础设施 - 产品推荐</div>
          <div class="title">双串口服务器</div>
          <div class="text">-40~85℃丨6kV防雷丨铝合金外壳丨导轨安装</div>
          <button class="btn" @click="handleClick({url: 'https://www.usr.cn/Product/154.html'})">了解更多
            <img alt="" class="icon" src="../../assets/images/arrow_right_line.svg"></button>
        </div>
      </div>
    </div>
  </div>


  <div v-if="showPassword" class="maskBox" @click.prevent="showPassword=false">
    <div class="cardBox" @click.stop>
      <div class="cardTitle">修改密码</div>
      <el-form ref="ruleFormRef"
               :model="passwordForm"
               :rules="rulesPassword"
               label-width="0">
        <el-form-item label="" prop="password">
          <el-input v-model="passwordForm.password" autocomplete="off" placeholder="请输入旧密码">
            <template #prepend>
              <img alt="" class="icon" src="../../assets/images/password.svg">
            </template>
          </el-input>
        </el-form-item>
        <!-- 修改密码输入框 -->
        <el-form-item label="" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" autocomplete="off" placeholder="请输入新密码">
            <template #prepend>
              <img alt="" class="icon" src="../../assets/images/passwordA.svg">
            </template>
          </el-input>
        </el-form-item>
        <!-- 修改确认密码输入框 -->
        <el-form-item label="" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" autocomplete="off" placeholder="请再次输入新密码">
            <template #prepend>
              <img alt="" class="icon" src="../../assets/images/passwordA.svg">
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="btnBox">
        <baseBtn length="236" text="确定" @click="submitPasswordForm(ruleFormRef)"></baseBtn>
      </div>
    </div>
  </div>
  <div v-if="showName" class="maskBox" @click.prevent="showName=false">
    <div class="cardBox" @click.stop>
      <div class="cardTitle">修改姓名</div>
      <el-form ref="ruleNameFormRef"
               :model="nameForm"
               :rules="rules"
               label-width="0">
        <el-form-item label=" " prop="name">
          <el-input v-model.number="nameForm.name" placeholder="请输入姓名">
            <template #prepend>
              <img alt="" class="icon" src="../../assets/images/userName.svg"/>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="btnBox">
        <baseBtn length="236" text="确定" @click="submitNameForm(ruleNameFormRef)"></baseBtn>
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
.card {
  padding: 16px 16px 0;
  background: var(--usr-banner-bg);
  margin-bottom: 8px;
  margin-right: 16px;
  border-radius: 6px;
  box-shadow: 0 2px 4px 0 var(--usr-banner-shadow);
  margin-left: 4px;

  .card-header {
    height: 24px;
    font-family: PingFangHK-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #25345C;
    letter-spacing: 0;
    line-height: 24px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .card-line {
      width: 2px;
      height: 14px;
      background-image: linear-gradient(179deg, #3C78FF 0%, #0F39A8 100%);
      border-radius: 1px;
      margin-right: 8px;
    }

    .card-title {
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 16px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      line-height: 24px;
    }
  }
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .infoItem {
    margin-bottom: 16px;

    .infoLabel {
      height: 22px;
      font-family: PingFangHK-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #7588BB;
      letter-spacing: 0;
      line-height: 22px;
      margin-bottom: 8px;
    }

    .infoContent {
      height: 40px;
    }
  }

  .mb-16 {
    margin-bottom: 16px;
  }


  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.mt-24 {
  margin-top: 24px;
}


.maskBox {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.50);

  .cardBox {
    width: 236px;
    box-shadow: 0 4px 10px 0 var(--usr-banner-shadow);
    border-radius: 6px;
    background: var(--usr-white);
    padding: 32px;

    .cardTitle {
      height: 28px;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 20px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      text-align: center;
      margin-bottom: 24px;
    }

    .icon {
      width: 18px;
      height: 18px;
    }

    .btnBox {
      button {
        width: 117px;
      }

      button:active {
        cursor: default;
      }
    }
  }
}

@media (min-width: 891px) {
  .bannerBox {
    grid-template-columns: repeat(3, 1fr); /* 使用弹性单位 */
    justify-content: center; /* 改为居中布局 */
    margin: 0 auto; /* 可选：居中容器 */
  }

  /* 移除 margin-right 计算 */
  .bannerItem:not(:last-child) {
    margin-right: 0;
  }
}

@media (max-width: 890px) {
  .bannerBox {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* 弹性最小宽度 */
    justify-content: space-around; /* 更灵活的间距 */
  }
}
::-webkit-scrollbar {
  display: none;
}
.mainBox{
  height: 100vh;
  overflow: auto;
}
.bannerBox {
  margin-right: 16px;
  margin-left: 4px;
  display: grid;
  gap: 12px;
  margin-bottom: 32px;

  .bannerItem {
    width: 100%;
    background: var(--usr-banner-bg);
    box-shadow: 0 2px 4px 0 var(--usr-banner-shadow);
    border-radius: 6px;
    display: flex;
    flex-direction: column;

    img {
      border-top-right-radius: 6px;
      border-top-left-radius: 6px;
    }

    /* 图片容器 */

    .img-container {
      position: relative;
      width: 100%;
      padding-top: calc(640 / 524 * 100%); /* 计算比例 ≈122.1374% */
      overflow: hidden;
    }

    /* 图片元素 */

    .img-container img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover; /* 保持比例填充容器 */
    }

    .contentBox {
      width: 230px;
      flex: 1;
      background: var(--usr-banner-bg);
      border-radius: 6px;
      padding: 24px 16px;

      .desc {
        height: 16px;
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 12px;
        color: var(--usr-btn-border-text);
        letter-spacing: 0;
        line-height: 16px;
        margin-bottom: 7px;
      }

      .title {
        height: 24px;
        font-family: PingFangHK-Semibold;
        font-weight: 600;
        font-size: 16px;
        color: var(--usr-account-current);
        letter-spacing: 0;
        line-height: 24px;
        margin-bottom: 9px;
      }

      .text {
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 11px;
        color: var(--usr-btn-border-text);
        letter-spacing: 0;
        line-height: 16px;
        margin-bottom: 16px;
      }

      .btn {
        width: 90px;
        height: 32px;
        line-height: 32px;
        border: 1.48px solid var(--usr-input-border);
        border-radius: 4.43px;
        text-align: center;
        font-family: PingFangHK-Regular;
        font-weight: 400;
        font-size: 12px;
        color: var(--usr-btn-border-text);
        letter-spacing: 0;
        cursor: default;
        background: var(--usr-white);
        display: flex;
        align-items: center;
        justify-content: center;

        .icon {
          width: 10px;
          height: 8px;
          margin-left: 8px;
        }
      }

      .btn:hover {
        cursor: pointer;
        background: var(--usr-input-border);
      }

      .btn.active {
        cursor: default;
      }
    }
  }
}

:deep(.el-input) {
  width: 100% !important;
  height: 40px;
}

:deep(.el-form-item__label) {
  padding: 0;
  border-radius: 4px;
  background: var(--usr-white);
  box-shadow: none;
}

:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
}

:deep(.el-input-group__prepend) {
  border-radius: 4px 0 0 4px;
  border: 1px solid var(--usr-input-border);
  background: var(--usr-white);
  box-shadow: none;
}

.maskBox {
  :deep(.el-input__wrapper) {
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
    height: 38px;
    border-left: 0 !important;
    padding-left: 0 !important;
    border: 1px solid var(--usr-input-border);
    box-shadow: none;
  }
}


:deep(.el-input__inner) {
  color: var(--usr-input-text) !important;
}

:deep(.el-input__inner::placeholder) {
  color: var(--usr-input-placeholder) !important;
}

:deep(.el-input-group__prepend) {
  padding: 0 10px 0 18px !important;
  border-right: 1px solid var(--usr-input-border) !important;
  border-right: 0 !important;
}

:deep(.el-form-item__error) {
  right: 0 !important;
  text-align: right !important;
}

.infoContent {
  :deep(.el-input) {
    border-radius: 4px;
  }

  :deep(.is-focus) {
    box-shadow: 0 0 0 1px var(--usr-input-border) !important;
  }

  :deep(.el-input__wrapper) {
    border-bottom-left-radius: 4px !important;
    border-top-left-radius: 4px !important;
    height: 38px;
    padding-left: 11px !important;
    border-right: 1px solid var(--usr-input-border) !important;
  }
}

</style>
