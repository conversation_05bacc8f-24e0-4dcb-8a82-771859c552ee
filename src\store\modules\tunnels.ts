import {defineStore} from 'pinia'
import {reactive, toRefs} from 'vue'
import TunnelManager from '../../models/TunnelManager.ts'
import type WiresockStateModel from '../../models/WiresockStateModel.ts'
import WiresockInstallDetails from '../../models/WiresockInstallDetails.ts'
import {
    deleteSelectedTunnelIDKeyFromStorage,
    getAllTunnelsFromStorage,
    getSelectedTunnelIDFromStorage,
    getSettingsFromStorage,
    saveSelectedTunnelIDToStorage,
    saveSettingsToStorage,
    saveTunnelsToStorage,
} from '../../utils/storageUtils.ts'

export const tunnelStore = defineStore('tunels', () => {
    const initData: any = reactive({
        // 核心数据
        tunnelManager: new TunnelManager(getAllTunnelsFromStorage()),
        wiresockState: null as WiresockStateModel | null,
        wiresockInstallDetails: null as WiresockInstallDetails | null,
        selectedTunnelID: getSelectedTunnelIDFromStorage() ?? null,
        settings: getSettingsFromStorage(),
        // 状态标记
        hasRunAutoConnect: false,
        isSettingsFirstChange: true,
        isTunnelManagerFirstChange: {
            current:true
        },
        isSelectedTunnelIDFirstChange: true,
        // 状态追踪
        tunnelManagerRef: {
            current:false
        },
        prevTunnelStatusRef: {
            current:false
        },
        wgConfig: {}
    })
    // 状态更新方法
    const setTunnelManagerRef= (flag: boolean) => {
        initData.tunnelManagerRef.current = flag
    }
    const setWgConfig= (wgConfig: any) => {
        initData.wgConfig = wgConfig
    }
    const setPrevTunnelStatusRef = (data: any) => {
        initData.prevTunnelStatusRef.current = data
    }
    const setIsSelectedTunnelIDFirstChange = (flag: boolean) => {
        initData.isTunnelManagerFirstChange.current = flag
    }
    const setWiresockState = (state: WiresockStateModel | null) => {
        initData.wiresockState = state
    }

    const setTunnelManager = (manager: TunnelManager) => {
        initData.tunnelManager = manager
    }

    const setSelectedTunnelID = (id: string | null) => {
        console.log(`Setting selected tunnel ID to ${id}`)
        initData.selectedTunnelID = id
    }

    const setWiresockInstallDetails = (details: WiresockInstallDetails) => {
        initData.wiresockInstallDetails = details
    }

    const setHasRunAutoConnect = (value: boolean) => {
        initData.hasRunAutoConnect = value
    }

    return {
        ...toRefs(initData),
        setWiresockState,
        setTunnelManager,
        setSelectedTunnelID,
        setWiresockInstallDetails,
        setHasRunAutoConnect,
        saveSettingsToStorage,
        deleteSelectedTunnelIDKeyFromStorage,
        saveSelectedTunnelIDToStorage,
        setIsSelectedTunnelIDFirstChange,
        setPrevTunnelStatusRef,
        setTunnelManagerRef,
        saveTunnelsToStorage,
        setWgConfig
    }
},{persist: true})
