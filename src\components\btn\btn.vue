<script setup lang="ts">
const props = defineProps({
  text: String,
  type: String,
  length: Number,
  height:Number,
})
</script>

<template>
  <div :class="['my-border-button',  'w-'+props.length,'h-'+props.height||40]" v-if="props.type=='border'">{{props.text}}</div>
  <div :class="['my-button',  'w-'+props.length,'h-'+props.height||40]"  v-else>{{props.text}}</div>
</template>

<style scoped>
.my-button {
  /* 默认状态 */
  height: 40px;
  line-height: 40px;
  background:var(--usr-primary)!important;
  border-radius: 4px;
  cursor: pointer;
  font-family: PingFangHK-Medium;
  font-weight: 500;
  font-size: 14px;
  color: var(--usr-white)!important;
  letter-spacing: 0;
  text-align: center;
}

/* 悬浮状态 */
.my-button:hover {
  background: var(--usr-btn-hover)!important;
}

/* 点击状态 */
.my-button:active {
  background: var(--usr-btn-active)!important;
}
.w-252{
  width: 252px;
}
.w-236{
  width: 236px;
}
.w-110{
  width: 110px;
}
.w-75{
  width: 75px;
}
.h-40{
  height: 40px;
}
.h-29{
  height: 29px;
  line-height: 29px;
  font-size: 12px;
}
.my-border-button {
  height: 40px;
  line-height: 40px;
  border: 1px solid var(--usr-input-border)!important;
  border-radius: 4px;
  font-family: PingFangHK-Medium;
  font-weight: 500;
  font-size: 14px;
  color: var(--usr-btn-border-text)!important;
  letter-spacing: 0;
  text-align: center;
}

/* 悬浮状态 */
.my-border-button:hover {
  border: 1px solid  var(--usr-btn-hover)!important;
  color:  var(--usr-btn-hover)!important;
}

/* 点击状态 */
.my-border-button:active {
  border: 1px solid  var(--usr-btn-active)!important;
  color:  var(--usr-btn-active)!important;
}
</style>
