import {createRouter, createWebHashHistory} from 'vue-router'
import login from '../view/Login/login.vue'
import checkWireSock from '../view/check/check.vue'
import main from '../view/main/main.vue'
import list from '../view/main/list.vue'
import speed from '../view/layout/speed.vue'
import info from '../view/layout/info.vue'


const routes = [{
    path: '/', component: checkWireSock
},{
    path: '/index', component: () => import('../view/layout/Layout.vue'), children: [{
        path: '/index', name: 'home', redirect: '/home',
    }, {
        path: '/home', component: main,
        meta:{
            menuIndex:'homePage'
        }
    }, {
        path: '/userInfo', component: info,
        meta:{
            menuIndex:'userInfo'
        }
    }, {
        path: '/list', component: list,
        meta:{
            menuIndex:'listPage'
        }
    }, {
        path: '/speed', component: speed,
        meta:{
            menuIndex:'speed'
        }
    }]
}, {
    path: '/login', component: login
} ]

const router = createRouter({
    // history: createWebHistory(),
    history: createWebHashHistory(), routes,
})

export default router;

