//! sdw interface management daemon
//!
//! This binary is meant to run as a daemon with root privileges
//! and communicate with the desktop client over HTTP.
use tracing::{error, info};

#[cfg(not(windows))]
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    use clap::Parser;
    use sdw::service::{config::Config, run_server, utils::logging_setup};
    info!("Starting sdw service");
    // parse config
    let config: Config = Config::parse();
    let _guard = logging_setup(&config.log_dir, &config.log_level);
    // 添加 panic hook 记录崩溃信息
    std::panic::set_hook(Box::new(|panic_info| {
        error!("Service panic: {}", panic_info);
    }));
    // run gRPC server
    run_server(config).await?;

    Ok(())
}

#[cfg(windows)]
fn main() -> anyhow::Result<()> {
    // defguard_client::service::windows::run()
    Ok(())
}
