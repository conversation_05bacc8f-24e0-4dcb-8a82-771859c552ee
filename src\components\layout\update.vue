<template>
  <div class="update-container" @click.stop.prevent>
    <div class="mainTop">
      <div class="updateTitle">有人异地组网</div>
      <div class="versionText"  v-if="status === 'idle'">V{{versionInfo.version}}</div>
    </div>
    <div class="mainBox">
      <div class="versionDesc"  v-if="status === 'idle'">{{versionInfo.note}}</div>
      <div v-if="status === 'downloading'">
        <el-progress :percentage="formatSize(progress)" :key="formatSize(progress)" />
      </div>
      <div class="btnBox"  v-if="status === 'idle'">
        <baseBtn v-if="force==false" text="取消" :length="110" type="border"  @click="closeMask"></baseBtn>
        <baseBtn text="更新" :length="110"  @click="startDownload(versionInfo.url)"></baseBtn>
      </div>
      <div class="btnBox"  v-if="status === 'ready_to_install'">
        <baseBtn v-if="force==false" text="取消" :length="110" type="border"  @click="closeMask"></baseBtn>
        <baseBtn text="安装" :length="110"  @click="performInstall"></baseBtn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {listen} from '@tauri-apps/api/event'
import {invoke} from '@tauri-apps/api/tauri'
import {getVersionAPI} from "../../api/UserApi.ts";
import {ElMessage} from "element-plus";

const status = ref('idle')
const progress = ref({received: 0, total: 0})
const error = ref(null)
let downloadPath = ref('')
const props = defineProps({
  force: {
    type: Boolean,
    default: false
  }
})
console.log(props.force)
/**
 * 监听下载进度事件
 */
listen('download_progress', (event) => {
  const payload:any = event.payload
  if (payload.done) {
    status.value = 'ready_to_install'
  } else {
    progress.value = payload
  }
})
// emit 定义
const emit = defineEmits(['close'])
/**
 * 关闭弹窗
 */
const  closeMask  = () => {
  console.log('closeMask')
  emit('close')
}
/**
 * 格式化大小
 */
const formatSize = (progress: any) => {
  let received = progress.received||0
  let total = progress.total||0
  if(received==0&&total==0){
    return 0
  }
  console.log(received,total)
  let num = (received/total)
  console.log(num)
  let percent = (num*100).toFixed(2)
  return percent
}
/**
 * 开始下载
 */
const startDownload = async (url: any) => {
  status.value = 'downloading'
  error.value = null
  console.log("startDownload :" ,url)
  downloadPath.value = await invoke('download_file_update', {
    request: {
      url: url,
      file_name: 'sdw' // 只传入文件名，后缀名在download_file_update中根据系统去处理
    }
  })
}
/**
 * 安装软件
 */
const performInstall = async () => {
  try {
    status.value = 'installing'
    ElMessage.success('安装进行中...')
    console.log(downloadPath.value)
    await invoke('install_package_update', {path: downloadPath.value})
    status.value = 'success'
    ElMessage.success('更新成功')
    // setTimeout(() => window.close(), 2000)
  } catch (err) {
    handleError(err)
  }
}
/**
 * 安装失败
 */
const handleError = (err: any) => {
  error.value = err
  status.value = 'idle'
  ElMessage.success(err)
  setTimeout(() => error.value = null, 5000)
}
const versionInfo: any = ref({})
/**
 * 获取版本号
 */
const getVersion = async () => {
  const version:any = await invoke('get_version')
  const osType = await invoke<string>('get_os_type');
  //判断osType 根据值设置type windows == 1 mac ==2
  let type = 1 //默认windows
  if (osType === 'macos-aarch64') {
    type = 2
  }
  if (osType ==='macos-x86_64'){
    type = 3
  }
  if (osType === 'linux'){
    type = 4
  }
  //version 1.0.14  转换为10014
  const arr = version.split('.')
  if (arr[1] < 10) {
    arr[1] = '0' + arr[1]
  }
  if (arr[2] < 10) {
    arr[2] = '0' + arr[1]
  }
  let versionNum = arr.join('') * 1
  const res: any = await getVersionAPI({
    type: type
  })
  console.log(res.result, version, versionNum,99998)
  if (res.msg === 'success') {
    if (res.result.v > versionNum) {
      versionInfo.value = res.result
    }
  }
}
onMounted(() => {
  getVersion()
})
</script>

<style scoped lang="less">
.update-container {
  width: 300px;
  background: var(--usr-white);
  box-shadow: 0 4px 10px 0 var(--usr-shadow);
  border-radius: 6px;
  position: relative;
  .mainTop{
    position: absolute;
    top: 0;
    width: 100%;
    height: 100px;
    border-radius: 6px 6px 0 0;
    background: url("../../assets/images/updateTop.svg");
    background-size: cover;
    .updateTitle{
      height: 28px;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 20px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      margin-top: 32px;
      margin-left: 32px;
      margin-bottom: 2px;
    }
    .versionText{
      height: 16px;
      font-family: PingFangHK-Regular;
      font-weight: 400;
      font-size: 12px;
      color: var(--usr-btn-border-text);
      letter-spacing: 0;
      line-height: 16px;
      margin-left: 32px;
    }
  }
  .mainBox{
    margin-top: 100px;
    padding:27px 27px;
    .btnBox{
      display: flex;
      justify-content: space-between;
      padding: 4px;
    }
  }

  .versionDesc{
    color: var(--Color-Text-text-color-secondary, #2A2D33);
    /* regular/base */
    font-family: "PingFang HK";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

progress {
  width: 100%;
  height: 20px;
  margin: 1rem 0;
}


.success {
  color: #28a745;
}

.error {
  color: #dc3545;
}

</style>
