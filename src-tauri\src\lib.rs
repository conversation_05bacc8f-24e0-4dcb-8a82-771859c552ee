use std::{fmt, path::PathBuf};

use chrono::NaiveDateTime;
// use database::models::NoId;
use serde::{Deserialize, Serialize};

// pub mod app_config;
pub mod appstate;
// pub mod commands;
// pub mod database;
// pub mod enterprise;
// pub mod error;
// pub mod events;
// pub mod log_watcher;
// pub mod periodic;
#[cfg(not(target_os = "windows"))]
pub mod service;
// pub mod tray;
// pub mod utils;
// pub mod wg_config;

// 添加以下基础类型定义
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Id(i64);

#[derive(Debug, Default, <PERSON>lone, Copy, Serialize, Deserialize)]
pub struct NoId;
#[cfg(not(target_os = "windows"))]
pub mod proto {
    // use crate::database::models::{location::Location, Id, NoId};

    tonic::include_proto!("sdw.proxy");

    impl DeviceConfig {
        #[must_use]
        pub(crate) fn into_location(self) -> super::Location {
            super::Location {
                name: self.network_name,
                address: self.assigned_ip, // Transforming assigned_ip to address
                pubkey: self.pubkey,
                endpoint: self.endpoint,
                allowed_ips: self.allowed_ips,
                dns: self.dns,
                route_all_traffic: false,
                mfa_enabled: self.mfa_enabled,
                keepalive_interval: self.keepalive_interval.into(),
            }
        }
    }
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct Location {
    pub name: String,
    pub address: String,
    pub pubkey: String,
    pub endpoint: String,
    pub allowed_ips: String,
    pub dns: Option<String>,
    pub route_all_traffic: bool,
    pub mfa_enabled: bool,
    pub keepalive_interval: i64,
}

pub const VERSION: &str = concat!(env!("CARGO_PKG_VERSION"));
// This must match tauri.bundle.identifier from tauri.conf.json.
static BUNDLE_IDENTIFIER: &str = "net.sdw";
// Returns the path to the user’s data directory.
#[must_use]
pub fn app_data_dir() -> Option<PathBuf> {
    dirs_next::data_dir().map(|dir| dir.join(BUNDLE_IDENTIFIER))
}

/// Location type used in commands to check if we using tunnel or location
#[derive(Clone, Copy, Debug, Deserialize, PartialEq, Serialize)]
pub enum ConnectionType {
    Tunnel,
    Location,
}

impl fmt::Display for ConnectionType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            ConnectionType::Tunnel => write!(f, "tunnel"),
            ConnectionType::Location => write!(f, "location"),
        }
    }
}

#[macro_use]
extern crate log;

// use self::database::models::Id;

/// Common fields for Tunnel and Location
#[derive(Debug, Serialize, Deserialize)]
pub struct CommonWireguardFields {
    pub instance_id: Id,
    // Native id of network from sdw
    pub network_id: Id,
    pub name: String,
    pub address: String,
    pub pubkey: String,
    pub endpoint: String,
    pub allowed_ips: String,
    pub dns: Option<String>,
    pub route_all_traffic: bool,
}
