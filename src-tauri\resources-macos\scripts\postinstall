#!/bin/bash

# macOS LaunchDaemon 安装脚本
# 需要管理员权限运行

set -e  # 遇到错误立即退出

# 配置变量
SERVICE_BINARY=sdw-service
DAEMON_PROPERTY_FILE=usr.sdw.plist
WIREGUARD_GO_BINARY=wireguard-go
DAEMON_NAME=usr.sdw
APP_BUNDLE=有人异地组网.app
PRODUCT_HOME=/Applications/${APP_BUNDLE}
BINARY_PATH=${PRODUCT_HOME}/Contents/MacOS
RESOURCES_PATH=${PRODUCT_HOME}/Contents/Resources/resources-macos/resources
LOG_DIR=/var/log
DAEMON_PLIST_PATH=/Library/LaunchDaemons/${DAEMON_PROPERTY_FILE}

# 日志函数
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_warn() {
    echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查是否以root权限运行
check_root_privileges() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要管理员权限运行"
        log_error "请使用: sudo $0"
        exit 1
    fi
}

# 检查文件是否存在
check_file_exists() {
    local file_path="$1"
    local description="$2"

    if [[ ! -f "$file_path" ]]; then
        log_error "$description 不存在: $file_path"
        return 1
    fi
    log_info "$description 存在: $file_path"
    return 0
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    # 创建 /usr/local/bin 目录
    if [[ ! -d "/usr/local/bin" ]]; then
        mkdir -p /usr/local/bin
        log_info "创建目录: /usr/local/bin"
    fi

    # 确保日志目录存在并设置权限
    if [[ ! -d "$LOG_DIR" ]]; then
        mkdir -p "$LOG_DIR"
        log_info "创建日志目录: $LOG_DIR"
    fi

    # 设置日志目录权限
    chmod 755 "$LOG_DIR"
}

# 安装二进制文件
install_binaries() {
    log_info "安装二进制文件..."

    # 检查源文件是否存在
    check_file_exists "${BINARY_PATH}/${SERVICE_BINARY}" "服务二进制文件" || exit 1
    check_file_exists "${BINARY_PATH}/${WIREGUARD_GO_BINARY}" "WireGuard二进制文件" || exit 1

    # 创建符号链接
    ln -sf "${BINARY_PATH}/${WIREGUARD_GO_BINARY}" "/usr/local/bin/${WIREGUARD_GO_BINARY}"
    log_info "创建WireGuard符号链接: /usr/local/bin/${WIREGUARD_GO_BINARY}"

    ln -sf "${BINARY_PATH}/${SERVICE_BINARY}" "/usr/local/bin/${SERVICE_BINARY}"
    log_info "创建服务符号链接: /usr/local/bin/${SERVICE_BINARY}"

    # 设置执行权限
    chmod +x "/usr/local/bin/${WIREGUARD_GO_BINARY}"
    chmod +x "/usr/local/bin/${SERVICE_BINARY}"
    log_info "设置二进制文件执行权限"
}

# 安装LaunchDaemon
install_launch_daemon() {
    log_info "安装LaunchDaemon..."

    # 检查plist文件是否存在
    check_file_exists "${RESOURCES_PATH}/${DAEMON_PROPERTY_FILE}" "LaunchDaemon plist文件" || exit 1

    # 停止现有服务（如果正在运行）
    if launchctl list | grep -q "$DAEMON_NAME"; then
        log_info "停止现有服务: $DAEMON_NAME"
        launchctl stop "$DAEMON_NAME" 2>/dev/null || true
        launchctl unload "$DAEMON_PLIST_PATH" 2>/dev/null || true
    fi

    # 复制plist文件到LaunchDaemons目录
    cp "${RESOURCES_PATH}/${DAEMON_PROPERTY_FILE}" "$DAEMON_PLIST_PATH"
    log_info "复制plist文件到: $DAEMON_PLIST_PATH"

    # 设置正确的权限
    chown root:wheel "$DAEMON_PLIST_PATH"
    chmod 644 "$DAEMON_PLIST_PATH"
    log_info "设置plist文件权限"

    # 加载并启动服务
    if launchctl load "$DAEMON_PLIST_PATH"; then
        log_info "成功加载LaunchDaemon: $DAEMON_NAME"
    else
        log_error "加载LaunchDaemon失败"
        exit 1
    fi

    # 等待服务启动
    sleep 2

    # 验证服务状态
    if launchctl list | grep -q "$DAEMON_NAME"; then
        log_info "LaunchDaemon启动成功: $DAEMON_NAME"
    else
        log_warn "LaunchDaemon可能未正常启动，请检查日志"
    fi
}

# 创建日志文件
setup_logging() {
    log_info "设置日志文件..."

    # 创建日志文件
    touch "$LOG_DIR/usr.sdw.out.log"
    touch "$LOG_DIR/usr.sdw.err.log"

    # 设置日志文件权限
    chown root:wheel "$LOG_DIR/usr.sdw.out.log"
    chown root:wheel "$LOG_DIR/usr.sdw.err.log"
    chmod 644 "$LOG_DIR/usr.sdw.out.log"
    chmod 644 "$LOG_DIR/usr.sdw.err.log"

    log_info "日志文件设置完成"
}

# 主安装流程
main() {
    log_info "=== macOS LaunchDaemon 安装开始 ==="

    # 检查权限
    check_root_privileges

    # 创建目录
    create_directories

    # 安装二进制文件
    install_binaries

    # 设置日志
    setup_logging

    # 安装LaunchDaemon
    install_launch_daemon

    log_info "=== 安装完成 ==="
    log_info "服务名称: $DAEMON_NAME"
    log_info "配置文件: $DAEMON_PLIST_PATH"
    log_info "日志文件: $LOG_DIR/usr.sdw.out.log, $LOG_DIR/usr.sdw.err.log"
    log_info "使用 'sudo launchctl list | grep $DAEMON_NAME' 检查服务状态"
}

# 执行主函数
main "$@"
