#!/bin/bash

SERVICE_BINARY=sdw-service
DAEMON_PROPERTY_FILE=usr.sdw.plist
WIREGUARD_GO_BINARY=wireguard-go
DAEMON_NAME=usr.sdw
APP_BUNDLE=有人异地组网.app
PRODUCT_HOME=/Applications/${APP_BUNDLE}
BINARY_PATH=${PRODUCT_HOME}/Contents/MacOS
RESOURCES_PATH=${PRODUCT_HOME}/Contents/Resources/resources-macos/resources

echo "Post installation process started"

mkdir -p /usr/local/bin

# Add wireguard-go shortcut to /usr/local/bin
ln -sf ${BINARY_PATH}/${WIREGUARD_GO_BINARY} /usr/local/bin/${WIREGUARD_GO_BINARY}

# Add service shortcut to /usr/local/bin
ln -sf ${BINARY_PATH}/${SERVICE_BINARY} /usr/local/bin/${SERVICE_BINARY}

# Launch daemon
ln -sf ${RESOURCES_PATH}/${DAEMON_PROPERTY_FILE} /Library/LaunchDaemons/${DAEMON_PROPERTY_FILE}
sudo launchctl load /Library/LaunchDaemons/${DAEMON_PROPERTY_FILE}
# Restart
sudo launchctl stop ${DAEMON_NAME}

echo "Post installation process finished"
