<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="100px" viewBox="0 0 300 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>椭圆形备份@3x</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#D3E8FF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M6,0 L294,0 C297.313708,-6.08718376e-16 300,2.6862915 300,6 L300,100 L300,100 L0,100 L0,6 C-4.05812251e-16,2.6862915 2.6862915,6.08718376e-16 6,0 Z" id="path-2"></path>
        <linearGradient x1="43.7787328%" y1="46.3614993%" x2="119.639246%" y2="21.7968933%" id="linearGradient-4">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#ACC5FF" offset="100%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <filter x="-60.0%" y="-0.0%" width="220.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="-16 1.95943488e-15" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <polygon id="path-6" points="0 0 15.461 0 15.461 15.2429 0 15.2429"></polygon>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="77.8103839%" id="linearGradient-8">
            <stop stop-color="#FF5900" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#F1FF67" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-10" points="0 0 13.5365903 0 13.5365903 26.1196976 0 26.1196976"></polygon>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#7AA1FF" offset="0%"></stop>
            <stop stop-color="#0F39A8" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-13" points="0 0 13.5364389 0 13.5364389 26.1196976 0 26.1196976"></polygon>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#7AA1FF" offset="0%"></stop>
            <stop stop-color="#0F39A8" offset="100%"></stop>
        </linearGradient>
        <path d="M3.93539581,0 C2.38396753,4.00559421 1.08337199,8.78397831 0.437868811,14.3571748 L0.437868811,14.3571748 C-0.0637966782,18.6871005 -0.170287039,23.4960158 0.307153155,28.7954325 L0.307153155,28.7954325 C0.307153155,28.7954325 1.74401598,41.8773038 6.18431072,50.1667767 L6.18431072,50.1667767 C6.18431072,50.1667767 13.2374045,46.7993347 21.5966454,50.1667767 L21.5966454,50.1667767 C21.5966454,50.1667767 26.6905185,43.1726278 27.735739,27.7593735 L27.735739,27.7593735 C27.9815248,24.1261599 28.0067595,20.2817304 27.7478517,16.4092723 L27.7478517,16.4092723 C27.3728643,10.8155548 26.4053665,5.162777 24.6520608,0 L24.6520608,0 L3.93539581,0 Z" id="path-16"></path>
        <linearGradient x1="34.5004051%" y1="12.2343837%" x2="65.4995949%" y2="69.4868129%" id="linearGradient-18">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#D2DFFF" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-19" points="0 0 20.716665 0 20.716665 15.0188511 0 15.0188511"></polygon>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-21">
            <stop stop-color="#7AA1FF" offset="0%"></stop>
            <stop stop-color="#0F39A8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-22">
            <stop stop-color="#7AA1FF" offset="0%"></stop>
            <stop stop-color="#0F39A8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-23">
            <stop stop-color="#2FCEFF" offset="0%"></stop>
            <stop stop-color="#3C78FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="-22.817691%" x2="130.034616%" y2="99.1747986%" id="linearGradient-24">
            <stop stop-color="#E8EFFF" offset="0%"></stop>
            <stop stop-color="#CDDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M28.0121006,0.76 C21.0191006,2.216 14.9511006,5.62 10.2671006,10.275 L10.2671006,10.275 C8.53110065,12.001 6.98510065,13.899 5.65310065,15.932 L5.65310065,15.932 C0.776100647,23.378 -1.23589935,32.65 0.770100647,42.008 L0.770100647,42.008 C2.77610065,51.365 8.41910065,59.043 15.9351006,63.907 L15.9351006,63.907 C17.9811006,65.232 20.1651006,66.347 22.4541006,67.232 L22.4541006,67.232 C28.6521006,69.627 35.6081006,70.324 42.6081006,68.865 L42.6081006,68.865 C52.1941006,66.869 60.0421006,61.213 64.9671006,53.692 L64.9671006,53.692 C69.8441006,46.247 71.8561006,36.975 69.8511006,27.617 L69.8511006,27.617 C67.8461006,18.261 62.2011006,10.582 54.6871006,5.718 L54.6871006,5.718 C49.0451006,2.066 42.3481006,0 35.3461006,0 L35.3461006,0 C32.9281006,0 30.4731006,0.247 28.0121006,0.76" id="path-25"></path>
        <filter x="-126.3%" y="-0.0%" width="352.6%" height="100.0%" filterUnits="objectBoundingBox" id="filter-27">
            <feGaussianBlur stdDeviation="-16 1.95943488e-15" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="43.7787328%" y1="46.3614993%" x2="119.639246%" y2="21.7968933%" id="linearGradient-28">
            <stop stop-color="#F3F6FF" offset="0%"></stop>
            <stop stop-color="#BCCBEE" offset="100%"></stop>
            <stop stop-color="#E0E7F9" offset="100%"></stop>
        </linearGradient>
        <filter x="-123.5%" y="-26.4%" width="347.1%" height="152.8%" filterUnits="objectBoundingBox" id="filter-29">
            <feGaussianBlur stdDeviation="15.6472331 3.34127157" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="PC客户端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="检查更新" transform="translate(-300, -145)">
            <g id="Frame-1" transform="translate(119, 145)">
                <g id="椭圆形备份" transform="translate(181, 0)">
                    <mask id="mask-3" fill="white">
                        <use xlink:href="#path-2"></use>
                    </mask>
                    <use id="蒙版" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    <circle id="椭圆形备份-2" fill="url(#linearGradient-4)" filter="url(#filter-5)" mask="url(#mask-3)" cx="210" cy="47" r="40"></circle>
                    <g id="编组" mask="url(#mask-3)">
                        <g transform="translate(5, -2.3501)">
                            <g>
                                <path d="M152.286734,93.6428024 C152.286734,106.266802 141.906734,116.499802 129.101734,116.499802 C116.297734,116.499802 105.917734,106.266802 105.917734,93.6428024 C105.917734,81.0198024 116.297734,70.7858024 129.101734,70.7858024 C141.906734,70.7858024 152.286734,81.0198024 152.286734,93.6428024" id="Fill-1" fill="#FFFFFF"></path>
                                <path d="M220.790634,93.6428024 C220.790634,104.952802 211.491634,114.119802 200.020634,114.119802 C188.550634,114.119802 179.251634,104.952802 179.251634,93.6428024 C179.251634,82.3338024 188.550634,73.1658024 200.020634,73.1658024 C211.491634,73.1658024 220.790634,82.3338024 220.790634,93.6428024" id="Fill-3" fill="#FFFFFF"></path>
                                <path d="M182.002634,75.8987024 C182.002634,84.7417024 174.731634,91.9107024 165.761634,91.9107024 C156.791634,91.9107024 149.520634,84.7417024 149.520634,75.8987024 C149.520634,67.0557024 156.791634,59.8867024 165.761634,59.8867024 C174.731634,59.8867024 182.002634,67.0557024 182.002634,75.8987024" id="Fill-5" fill="#FFFFFF"></path>
                                <path d="M147.481134,66.1868024 C147.481134,72.3468024 142.416134,77.3408024 136.167134,77.3408024 C129.918134,77.3408024 124.852134,72.3468024 124.852134,66.1868024 C124.852134,60.0258024 129.918134,55.0318024 136.167134,55.0318024 C142.416134,55.0318024 147.481134,60.0258024 147.481134,66.1868024" id="Fill-7" fill="#FFFFFF"></path>
                                <g transform="translate(157.2828, 49.3501)">
                                    <mask id="mask-7" fill="white">
                                        <use xlink:href="#path-6"></use>
                                    </mask>
                                    <g id="Clip-10"></g>
                                    <path d="M15.461,7.6209 C15.461,11.8309 12,15.2429 7.731,15.2429 C3.461,15.2429 0,11.8309 0,7.6209 C0,3.4119 3.461,-0.0001 7.731,-0.0001 C12,-0.0001 15.461,3.4119 15.461,7.6209" id="Fill-9" fill="#FFFFFF" mask="url(#mask-7)"></path>
                                </g>
                                <path d="M164.967434,75.7239024 C164.967434,83.1829024 158.833434,89.2309024 151.266434,89.2309024 C143.699434,89.2309024 137.566434,83.1829024 137.566434,75.7239024 C137.566434,68.2639024 143.699434,62.2159024 151.266434,62.2159024 C158.833434,62.2159024 164.967434,68.2639024 164.967434,75.7239024" id="Fill-11" fill="#FFFFFF"></path>
                                <path d="M83.3707341,133.543202 C80.1477341,156.128202 58.9647341,171.860202 36.0567341,168.683202 C13.1487341,165.506202 -2.80926593,144.621202 0.413734067,122.037202 C3.63673407,99.4522024 24.8197341,83.7202024 47.7277341,86.8972024 C70.6357341,90.0742024 86.5937341,110.958202 83.3707341,133.543202" id="Fill-13" fill="#FFFFFF"></path>
                                <path d="M123.559234,102.841102 C124.282234,119.927102 110.818234,134.355102 93.4872341,135.067102 C76.1572341,135.779102 61.5222341,122.504102 60.8012341,105.419102 C60.0792341,88.3331024 73.5422341,73.9051024 90.8722341,73.1941024 C108.204234,72.4821024 122.837234,85.7561024 123.559234,102.841102" id="Fill-15" fill="#FFFFFF"></path>
                                <path d="M290.171534,125.201402 C290.171534,149.373402 270.295534,168.968402 245.777534,168.968402 C221.260534,168.968402 201.385534,149.373402 201.385534,125.201402 C201.385534,101.029402 221.260534,81.4344024 245.777534,81.4344024 C270.295534,81.4344024 290.171534,101.029402 290.171534,125.201402" id="Fill-17" fill="#FFFFFF"></path>
                                <path d="M167.094434,114.946602 C167.094434,132.048602 153.032434,145.910602 135.687434,145.910602 C118.342434,145.910602 104.281434,132.048602 104.281434,114.946602 C104.281434,97.8456024 118.342434,83.9836024 135.687434,83.9836024 C153.032434,83.9836024 167.094434,97.8456024 167.094434,114.946602" id="Fill-19" fill="#FFFFFF"></path>
                                <g transform="translate(202.8115, 72.5709) rotate(-310) translate(-202.8115, -72.5709)translate(173.1751, -5.0101)">
                                    <polygon id="Fill-1" fill="url(#linearGradient-8)" points="17.2448774 113 27.3019077 113 27.3019077 66.1581218 17.2448774 66.1581218"></polygon>
                                    <path d="M46.5921443,132.305046 C46.5921443,144.929046 36.2121443,155.162046 23.4071443,155.162046 C10.6031443,155.162046 0.22314433,144.929046 0.22314433,132.305046 C0.22314433,119.682046 10.6031443,109.448046 23.4071443,109.448046 C36.2121443,109.448046 46.5921443,119.682046 46.5921443,132.305046" id="Fill-1" fill="#FFFFFF"></path>
                                    <path d="M41.7865443,104.849046 C41.7865443,111.009046 36.7215443,116.003046 30.4725443,116.003046 C24.2235443,116.003046 19.1575443,111.009046 19.1575443,104.849046 C19.1575443,98.6880459 24.2235443,93.6940459 30.4725443,93.6940459 C36.7215443,93.6940459 41.7865443,98.6880459 41.7865443,104.849046" id="Fill-7" fill="#FFFFFF"></path>
                                    <path d="M59.2728443,114.386146 C59.2728443,121.845146 53.1388443,127.893146 45.5718443,127.893146 C38.0048443,127.893146 31.8718443,121.845146 31.8718443,114.386146 C31.8718443,106.926146 38.0048443,100.878146 45.5718443,100.878146 C53.1388443,100.878146 59.2728443,106.926146 59.2728443,114.386146" id="Fill-11" fill="#FFFFFF"></path>
                                    <g transform="translate(0, 49.0829)">
                                        <path d="M26.1157263,20.8618047 C26.1157263,18.2340033 24.4689109,16.1031594 22.4380236,16.1031594 C20.4066317,16.1031594 18.760321,18.2340033 18.760321,20.8618047 C18.760321,20.8618047 18.2848996,32.1135486 22.4380236,38.1031594 C26.5901383,32.1135486 26.1157263,20.8618047 26.1157263,20.8618047" id="Fill-2" fill="url(#linearGradient-9)"></path>
                                        <g>
                                            <mask id="mask-11" fill="white">
                                                <use xlink:href="#path-10"></use>
                                            </mask>
                                            <g id="Clip-5"></g>
                                            <path d="M9.59392349,0 C9.59392349,0 -4.17815686,6.61426059 1.2785909,26.1196976 C1.2785909,26.1196976 4.19319673,16.3146756 13.5365903,13.7755803 L9.59392349,0 Z" id="Fill-4" fill="url(#linearGradient-12)" mask="url(#mask-11)"></path>
                                        </g>
                                        <g transform="translate(31.4636, 0)">
                                            <mask id="mask-14" fill="white">
                                                <use xlink:href="#path-13"></use>
                                            </mask>
                                            <g id="Clip-7"></g>
                                            <path d="M3.9426668,0 C3.9426668,0 17.7147471,6.61426059 12.2574947,26.1196976 C12.2574947,26.1196976 9.34339356,16.3146756 0,13.7755803 L3.9426668,0 Z" id="Fill-6" fill="url(#linearGradient-15)" mask="url(#mask-14)"></path>
                                        </g>
                                        <path d="M26.9693651,17.8436885 L17.6083073,17.8436885 C17.2388715,17.8436885 16.9188957,17.5899291 16.8371354,17.2325638 L16.3228525,14.9847663 C16.2113152,14.4947654 16.587312,14.0272876 17.0945291,14.0272876 L27.4831432,14.0272876 C27.9903604,14.0272876 28.3668618,14.4947654 28.2543151,14.9847663 L27.7400323,17.2325638 C27.6587766,17.5899291 27.3382962,17.8436885 26.9693651,17.8436885" id="Fill-8" fill="#0F39A8"></path>
                                    </g>
                                    <g transform="translate(8.4725, 15.0189)">
                                        <mask id="mask-17" fill="white">
                                            <use xlink:href="#path-16"></use>
                                        </mask>
                                        <g id="Clip-11"></g>
                                        <path d="M3.93539581,0 C2.38396753,4.00559421 1.08337199,8.78397831 0.437868811,14.3571748 L0.437868811,14.3571748 C-0.0637966782,18.6871005 -0.170287039,23.4960158 0.307153155,28.7954325 L0.307153155,28.7954325 C0.307153155,28.7954325 1.74401598,41.8773038 6.18431072,50.1667767 L6.18431072,50.1667767 C6.18431072,50.1667767 13.2374045,46.7993347 21.5966454,50.1667767 L21.5966454,50.1667767 C21.5966454,50.1667767 26.6905185,43.1726278 27.735739,27.7593735 L27.735739,27.7593735 C27.9815248,24.1261599 28.0067595,20.2817304 27.7478517,16.4092723 L27.7478517,16.4092723 C27.3728643,10.8155548 26.4053665,5.162777 24.6520608,0 L24.6520608,0 L3.93539581,0 Z" id="Fill-10" fill="url(#linearGradient-18)" mask="url(#mask-17)"></path>
                                    </g>
                                    <g transform="translate(12.4078, -0)">
                                        <g>
                                            <mask id="mask-20" fill="white">
                                                <use xlink:href="#path-19"></use>
                                            </mask>
                                            <g id="Clip-13"></g>
                                            <path d="M8.42737458,0.843862532 C6.3434945,3.18175214 2.77076814,7.86804211 0,15.0188511 L20.716665,15.0188511 C18.8185122,9.42813672 16.0023216,4.4110086 12.0152417,0.671186035 C10.9891996,-0.291798254 9.36307661,-0.205710261 8.42737458,0.843862532" id="Fill-12" fill="url(#linearGradient-21)" mask="url(#mask-20)"></path>
                                        </g>
                                        <path d="M9.72408398,43.1817871 C9.72408398,43.1817871 3.36948622,57.5009231 9.72408398,74.5258246 C9.72408398,74.5258246 16.7852529,60.3198043 9.72408398,43.1817871" id="Fill-14" fill="url(#linearGradient-22)"></path>
                                        <path d="M18.3102857,29.8948062 C18.3102857,34.272781 14.731503,37.8214082 10.3169476,37.8214082 C5.90239225,37.8214082 2.32411426,34.272781 2.32411426,29.8948062 C2.32411426,25.5168314 5.90239225,21.9677037 10.3169476,21.9677037 C14.731503,21.9677037 18.3102857,25.5168314 18.3102857,29.8948062" id="Fill-16" fill="url(#linearGradient-23)"></path>
                                        <path d="M15.8662057,29.8948062 C15.8662057,32.9339125 13.3821036,35.3979311 10.317099,35.3979311 C7.25259917,35.3979311 4.76799233,32.9339125 4.76799233,29.8948062 C4.76799233,26.8556999 7.25259917,24.3916813 10.317099,24.3916813 C13.3821036,24.3916813 15.8662057,26.8556999 15.8662057,29.8948062" id="Fill-18" fill="url(#linearGradient-24)"></path>
                                    </g>
                                </g>
                            </g>
                            <g transform="translate(138.1138, 76.2931)">
                                <mask id="mask-26" fill="white">
                                    <use xlink:href="#path-25"></use>
                                </mask>
                                <g id="Clip-22"></g>
                                <path d="M28.0121006,0.76 C21.0191006,2.216 14.9511006,5.62 10.2671006,10.275 L10.2671006,10.275 C8.53110065,12.001 6.98510065,13.899 5.65310065,15.932 L5.65310065,15.932 C0.776100647,23.378 -1.23589935,32.65 0.770100647,42.008 L0.770100647,42.008 C2.77610065,51.365 8.41910065,59.043 15.9351006,63.907 L15.9351006,63.907 C17.9811006,65.232 20.1651006,66.347 22.4541006,67.232 L22.4541006,67.232 C28.6521006,69.627 35.6081006,70.324 42.6081006,68.865 L42.6081006,68.865 C52.1941006,66.869 60.0421006,61.213 64.9671006,53.692 L64.9671006,53.692 C69.8441006,46.247 71.8561006,36.975 69.8511006,27.617 L69.8511006,27.617 C67.8461006,18.261 62.2011006,10.582 54.6871006,5.718 L54.6871006,5.718 C49.0451006,2.066 42.3481006,0 35.3461006,0 L35.3461006,0 C32.9281006,0 30.4731006,0.247 28.0121006,0.76" id="Fill-21" mask="url(#mask-26)"></path>
                            </g>
                        </g>
                    </g>
                    <g id="编组" mask="url(#mask-3)">
                        <g transform="translate(140.0932, 42.9855) scale(-1, 1) translate(-140.0932, -42.9855)translate(21.9996, 16.7268)">
                            <path d="M116.73155,49.08195 L115.36155,48.34795 C115.27555,48.30195 115.20555,48.23095 115.15955,48.14595 L114.42455,46.77495 C114.23955,46.42895 113.74255,46.42895 113.55555,46.77495 L112.82155,48.14595 C112.77555,48.23095 112.70555,48.30195 112.62055,48.34795 L111.24955,49.08195 C110.90255,49.26795 110.90255,49.76495 111.24955,49.95095 L112.62055,50.68595 C112.70555,50.73195 112.77555,50.80195 112.82155,50.88695 L113.55555,52.25795 C113.74255,52.60395 114.23955,52.60395 114.42455,52.25795 L115.15955,50.88695 C115.20555,50.80195 115.27555,50.73195 115.36155,50.68595 L116.73155,49.95095 C117.07855,49.76495 117.07855,49.26795 116.73155,49.08195" id="Fill-5" fill="#EBF5FF"></path>
                            <path d="M35.98645,3.23475 L34.25845,2.30875 C34.15145,2.25075 34.06245,2.16275 34.00545,2.05475 L33.07945,0.32775 C32.84445,-0.10925 32.21745,-0.10925 31.98345,0.32775 L31.05745,2.05475 C31.00045,2.16275 30.91245,2.25075 30.80345,2.30875 L29.07745,3.23475 C28.63945,3.46875 28.63945,4.09475 29.07745,4.32975 L30.80345,5.25575 C30.91245,5.31375 31.00045,5.40175 31.05745,5.50975 L31.98345,7.23675 C32.21745,7.67375 32.84445,7.67375 33.07945,7.23675 L34.00545,5.50975 C34.06245,5.40175 34.15145,5.31375 34.25845,5.25575 L35.98645,4.32975 C36.42345,4.09475 36.42345,3.46875 35.98645,3.23475" id="Fill-7" fill="#C1DDF7"></path>
                            <path d="M235.79115,4.92025 L233.70215,3.80025 C233.57215,3.73025 233.46515,3.62325 233.39515,3.49325 L232.27515,1.40425 C231.99215,0.87625 231.23415,0.87625 230.95115,1.40425 L229.83115,3.49325 C229.76215,3.62325 229.65415,3.73025 229.52415,3.80025 L227.43615,4.92025 C226.90715,5.20325 226.90715,5.96125 227.43615,6.24425 L229.52415,7.36425 C229.65415,7.43425 229.76215,7.54125 229.83115,7.67125 L230.95115,9.75925 C231.23415,10.28825 231.99215,10.28825 232.27515,9.75925 L233.39515,7.67125 C233.46515,7.54125 233.57215,7.43425 233.70215,7.36425 L235.79115,6.24425 C236.31915,5.96125 236.31915,5.20325 235.79115,4.92025" id="Fill-9" fill="#C1D6F7"></path>
                            <path d="M8.01225,35.87875 L6.10025,34.85375 C5.98025,34.78975 5.88325,34.69175 5.81925,34.57175 L4.79325,32.65975 C4.53425,32.17575 3.84025,32.17575 3.58125,32.65975 L2.55525,34.57175 C2.49125,34.69175 2.39425,34.78975 2.27425,34.85375 L0.36225,35.87875 C-0.12075,36.13775 -0.12075,36.83175 0.36225,37.09075 L2.27425,38.11675 C2.39425,38.18075 2.49125,38.27775 2.55525,38.39775 L3.58125,40.30975 C3.84025,40.79375 4.53425,40.79375 4.79325,40.30975 L5.81925,38.39775 C5.88325,38.27775 5.98025,38.18075 6.10025,38.11675 L8.01225,37.09075 C8.49625,36.83175 8.49625,36.13775 8.01225,35.87875" id="Fill-11" fill="#84B4FE"></path>
                            <path d="M154.69635,12.95785 L153.29735,12.20785 C153.21035,12.16085 153.13835,12.08885 153.09235,12.00185 L152.34135,10.60285 C152.15135,10.24885 151.64335,10.24885 151.45435,10.60285 L150.70335,12.00185 C150.65635,12.08885 150.58535,12.16085 150.49835,12.20785 L149.09835,12.95785 C148.74435,13.14785 148.74435,13.65485 149.09835,13.84485 L150.49835,14.59585 C150.58535,14.64285 150.65635,14.71385 150.70335,14.80085 L151.45435,16.20085 C151.64335,16.55485 152.15135,16.55485 152.34135,16.20085 L153.09235,14.80085 C153.13835,14.71385 153.21035,14.64285 153.29735,14.59585 L154.69635,13.84485 C155.05035,13.65485 155.05035,13.14785 154.69635,12.95785" id="Fill-13" fill="#84B4FE"></path>
                        </g>
                    </g>
                    <circle id="椭圆形备份-3" fill="url(#linearGradient-4)" filter="url(#filter-27)" mask="url(#mask-3)" cx="60" cy="41" r="19"></circle>
                    <circle fill="url(#linearGradient-28)" opacity="0.600000024" filter="url(#filter-29)" mask="url(#mask-3)" cx="118" cy="50" r="19"></circle>
                    <g id="编组" mask="url(#mask-3)">
                        <g transform="translate(75, 17.9998)">
                            <g>
                                <path d="M1.675,5.15 C3.27,3.698 4.962,3.956 5.84,4.244 C6.61,2.26 8.268,0 11.829,0 C18.175,0 18.683,6.243 18.683,6.243 C23.151,4.89 24.014,8.843 24.014,9 L0,9 C0,6.451 1.675,5.15 1.675,5.15" id="Fill-1" fill="#FFFFFF"></path>
                            </g>
                            <g transform="translate(75.2773, 3.9952)">
                                <path d="M6.342,5.603 C7.504,5.603 8.593,5.918 9.537,6.464 C11.342,3.317 14.672,1.197 18.493,1.197 C22.061,1.197 25.207,3.041 27.078,5.849 C28.066,2.467 31.122,0 34.744,0 C38.856,0 42.238,3.177 42.693,7.264 C43.024,7.211 43.361,7.175 43.705,7.175 C46.213,7.175 48.386,8.627 49.478,10.754 L0,10.754 C0.676,7.803 3.256,5.603 6.342,5.603" id="Fill-3" fill="#FFFFFF"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>