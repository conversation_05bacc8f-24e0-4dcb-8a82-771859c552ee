
<script setup>
import {ref, onMounted, onUnmounted, reactive} from 'vue'
import { invoke} from '@tauri-apps/api/tauri'
import {listen} from '@tauri-apps/api/event'
import Chart from 'chart.js/auto'

// 响应式数据
const serverAddress = ref('**********')
const bandwidth = ref(0)
const jitter = ref(0)
const packetLoss = ref(0)
const isTesting = ref(false)
const chartCanvas = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (chartInstance) chartInstance.destroy()

  chartInstance = new Chart(chartCanvas.value, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: '带宽 (Mbps)',
        data: [],
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  })
}

// 启动测试
const startTest = async () => {
  if (!serverAddress.value) return

  isTesting.value = true
  bandwidth.value = 0
  jitter.value = 0
  packetLoss.value = 0

  try {
    await invoke('start_iperf', {
      server: serverAddress.value
    })
  } catch (err) {
    console.error('测试失败:', err)
  } finally {
    isTesting.value = false
  }
}

// 监听数据更新
onMounted(async () => {
  // const unlisten = await listen('iperf_update', (event) => {
  //   const data = event.payload
  //   bandwidth.value = data.bitrate.toFixed(2)
  //   jitter.value = data.jitter.toFixed(2)
  //   packetLoss.value = data.packet_loss.toFixed(1)
  //   console.log('收到iperf_update事件:',  bandwidth.value, jitter.value, packetLoss.value)
  // })
  // 实时吞吐量监听
  listen('realtime_throughput', (event) => {
    console.log('当前吞吐量:', event.payload.toFixed(2), 'Mbps');
  });

// 完整统计监听
  listen('complete_stats', (event) => {
    const stats = event.payload;
    console.log(`
    总上传: ${stats.total_upload_mb.toFixed(2)} MB
    平均上传: ${stats.avg_upload_mbps.toFixed(2)} Mbps
    带宽历史: ${stats.bandwidth_history.map(v => v.toFixed(2))}
  `);
  });

// 历史数据监听
  listen('bandwidth_history', (event) => {
    console.log(event.payload)
  });
})

onUnmounted(() => {
  if (chartInstance) chartInstance.destroy()
})
const  status = ref(0)
const form = reactive({
  size: 5,
  num: 10
})
const sizeList = [
  {
    value: 5,
    label: '5 MB'
  },
  {
    value: 10,
    label: '10 MB'
  },
  {
    value: 15,
    label: '15 MB'
  },
  {
    value: 20,
    label: '20 MB'
  }
]
const numList = [
  {
    value: 10,
    label: '10'
  },
  {
    value: 20,
    label: '20'
  },
  {
    value: 30,
    label: '30'
  },
  {
    value: 40,
    label: '40'
  }
]
const activities = [
  {
    content: '5',
    timestamp: '2018-04-15',
  },
  {
    content: '8',
    timestamp: '2018-04-13',
  },
  {
    content: '10',
    timestamp: '2018-04-11',
  },
]
</script>
<template>
  <div class="circleBox">
    <div v-if="status==0">开始</div>

    <div v-if="status==1">正在测速</div>

    <div v-if="status==2">
      <div>上行</div>
      <div>下行</div>
      <div>丢包率</div>
    </div>

  </div>
  <div class="controls" v-if="status==3">
    <el-button  type="primary" @click="startTest">重新测速</el-button>
  </div>
  <div class="formBox">
    <el-form-item label="包大小">
      <el-select
        v-model="form.size"
        placeholder="Select"
        size="large"
        style="width: 240px"
      >
        <el-option
          v-for="item in sizeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="包数量">
      <el-select
        v-model="form.num"
        placeholder="Select"
        size="large"
        style="width: 240px"
      >
        <el-option
          v-for="item in numList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>
  <el-divider />
  <div class="iperf-container" v-if="status==2">
    <el-timeline style="max-width: 600px">
      <el-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :timestamp="activity.timestamp"
      >
        包：{{ bandwidth }} Mbps
      </el-timeline-item>
    </el-timeline>
  </div>
</template>


<style scoped>
.circleBox{
  width: 200px;
  height: 200px;
  border-radius: 100px;
  border: 1px solid #d00;
  margin: 50px auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.formBox{
  display: flex;
  justify-content: space-around;
}
.iperf-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stat-item {
  background: var(--usr-white);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.label {
  display: block;
  color: #666;
  font-size: 0.9em;
}

.value {
  font-size: 1.5em;
  font-weight: bold;
  color: #2c3e50;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.server-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.test-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.test-button:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.chart-container {
  height: 300px;
}
</style>
