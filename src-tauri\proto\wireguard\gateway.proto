syntax = "proto3";
package gateway;

import "google/protobuf/empty.proto";

message ConfigurationRequest {
  optional string name = 1;
}

message Configuration {
  string name = 1;
  string prvkey = 2;
  // string address = 3; // obsolete, use `addresses`
  uint32 port = 4;
  repeated Peer peers = 5;
  repeated string addresses = 6;
}

enum UpdateType {
  CREATE = 0;
  MODIFY = 1;
  DELETE = 2;
}

message Peer {
  string pubkey = 1;
  repeated string allowed_ips = 2;
  optional string preshared_key = 3;
  optional uint32 keepalive_interval = 4;
}

message Update {
  UpdateType update_type = 1;
  oneof update {
    Peer peer = 2;
    Configuration network = 3;
  }
}

message PeerStats {
  string public_key = 1;
  string endpoint = 2;
  uint64 upload = 3;
  uint64 download = 4;
  uint32 keepalive_interval = 5;
  uint64 latest_handshake = 6;
  string allowed_ips = 7;
}

/*
 * Allow empty messages to keep the connection alive.
 */
message StatsUpdate {
  uint64 id = 1;
  oneof payload {
    google.protobuf.Empty empty = 2;
    PeerStats peer_stats = 3;
  }
}

service GatewayService {
  rpc Config (ConfigurationRequest) returns (Configuration);
  rpc Updates (google.protobuf.Empty) returns (stream Update);
  rpc Stats (stream StatsUpdate) returns (google.protobuf.Empty);
}
