use std::collections::{HashMap, HashSet};

use tauri::{
    async_runtime::{spawn, <PERSON><PERSON><PERSON><PERSON><PERSON>},
    AppH<PERSON><PERSON>,
};
use tokio_util::sync::CancellationToken;
use tonic::transport::Channel;
#[cfg(not(target_os = "windows"))] // 添加条件编译属性
use crate::{
    service::{
        proto::desktop_daemon_service_client::DesktopDaemonServiceClient, utils::setup_client,
    }
};

pub struct AppState {
    #[cfg(not(target_os = "windows"))] // 仅 macOS 生效
    pub client: DesktopDaemonServiceClient<Channel>,
    pub log_watchers: std::sync::Mutex<HashMap<String, CancellationToken>>,
    pub last_static_ip_adapter: std::sync::Mutex<Option<String>>,
}

impl AppState {
    #[must_use]
    pub fn new() -> Self {
        AppState {
            #[cfg(not(target_os = "windows"))] // 仅 macOS 初始化
            client: setup_client().expect("Failed to setup gRPC client"),
            log_watchers: std::sync::Mutex::new(HashMap::new()),
            last_static_ip_adapter: std::sync::Mutex::new(None),
        }
    }

}
