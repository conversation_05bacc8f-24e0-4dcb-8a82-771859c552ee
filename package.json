{"name": "sdw-desktop", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@novnc/novnc": "1.6.0-beta", "@tauri-apps/api": "^1.5.6", "@types/crypto-js": "^4.2.2", "chart.js": "^4.4.8", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.4", "moment": "^2.30.1", "mqtt": "^5.10.4", "pinia-plugin-persistedstate": "^4.2.0", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "vue": "^3.4.38", "vue-router": "^4.2.5"}, "devDependencies": {"@tauri-apps/cli": "^1.5.14", "@vitejs/plugin-vue": "^5.2.1", "axios": "^1.7.9", "less": "^4.2.2", "pinia": "^2.1.7", "sass-embedded": "^1.86.3", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}