<script lang="ts" setup>
import {onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import { FormInstance, FormRules} from 'element-plus'
import {ElNotification,ElMessage} from 'element-plus'
import {getVersionAPI, signInAPI} from "../../api/UserApi.ts";
import moment from 'moment'
import {useMqttStore, useTokenStore} from '../../store'
import {invoke} from "@tauri-apps/api/tauri";
const mqttStore = useMqttStore()
const router = useRouter()
const tokenStore = useTokenStore()
import LoginInfo from '../../components/btn/info.vue'
import { h } from 'vue'
import update from '../../components/layout/update.vue'
// 登录表单数据
interface LoginForm {
  clientName: string
  password: string
}

const loginForm = ref<LoginForm>({
  clientName: '',
  password: '',
})
// 表单验证规则
const loginRules = ref<FormRules<LoginForm>>({
  clientName: [{required: true, message: '请输入账号', trigger: 'blur'}],
  password: [{required: true, message: '请输入密码', trigger: 'blur'}],
})
// 表单引用
const loginFormRef = ref<FormInstance>()

interface User {
  clientName: string
  password: string
  currentAccount: boolean
  token: string
  id: string
  forceChangePwd: boolean
}
/**
 * 获取本地账户列表
 */
const getUsers = (): User[] => {
  try {
    const data = localStorage.getItem('userList')
    return data ? JSON.parse(data) : []
  } catch {
    return []
  }
}
/**
 * 保存本地账户列表
 */
const saveUsers = (users: User[]) => {
  localStorage.setItem('userList', JSON.stringify(users))
}
/**
 * 处理登录流程的核心函数
 * 功能包含：
 * 1. 执行表单验证
 * 2. 调用登录接口进行认证
 * 3. 管理本地用户数据存储
 * 4. 处理登录成功后的状态更新和路由跳转
 */
const handleLogin = async () => {
    const valid = await loginFormRef.value?.validate()
    if (!valid) {
      ElMessage.error('请填写完整信息')
      return
    }

    const {clientName, password} = loginForm.value
  let clientNameStr = clientName
  let passwordStr = password
    const res: any = await signInAPI({clientName:clientNameStr, password:passwordStr})
    if (res.msg !== 'success') { // 根据实际接口规范调整
      ElMessage.error(res.msg || '登录失败')
      return
    }

    const users = getUsers()
    const currentUser = users.find(u => u.clientName === clientName)
    const newUser = {
      ...res.result,
      password,
      currentAccount: true,
    }
    localStorage.setItem('loginTime', moment().format('YYYY-MM-DD HH:mm:ss'))
    const updatedUsers = users.map(user => ({
      ...user,
      // 仅当匹配客户端名称时更新密码，否则保留原密码
      password: user.clientName === clientName ? password : user.password,
      // 设置当前账户标志（true/false）
      currentAccount: user.clientName === clientName
    }));

    if (!currentUser) {
      updatedUsers.push(newUser)
    }
    saveUsers(updatedUsers)
    tokenStore.loginToken(newUser.token)
    // 调用 Rust 后端存储 token
    await invoke('store_token', { token:newUser.token });
    ElMessage.success('登录成功')
    router.push('/index')
}
// 页面加载时检查是否记住密码
onMounted(() => {
  mqttStore.disconnect()
  mqttStore.setDna()
  mqttStore.loading?.close()
  let list: any = localStorage.getItem('userList')
  list = JSON.parse(list)
  for (let i in list) {
    if (list[i].currentAccount == true) {
      loginForm.value.clientName = list[i].clientName
      loginForm.value.password = list[i].password
    }
  }
  checkVersion()
})
/**
 * 忘记密码
 */
const forget = () => {
  ElNotification({
    title:"忘记密码",
    icon: LoginInfo,
    duration:3000,
    message: h('p', { style: 'font-weight: 400;font-size: 14px;color: #7588BB;letter-spacing: 0;line-height: 22px;' }, '请联系管理员重置密码'),
  })
}
const showPassword = ref(false)

const checkVersion =async  () => {
    const version: any = await invoke('get_version')
    const osType = await invoke<string>('get_os_type');
    //判断osType 根据值设置type windows == 1 mac ==2
    let type = 1 //默认windows
    if (osType === 'macos-aarch64') {
      type = 2
    }
    if (osType ==='macos-x86_64'){
      type = 3
    }
    if (osType === 'linux'){
      type = 4
    }
    //version 1.0.14  转换为10014
    const arr = version.split('.')
    if (arr[1] < 10) {
      arr[1] = '0' + arr[1]
    }
    if (arr[2] < 10) {
      arr[2] = '0' + arr[1]
    }
    console.log("type :",type)
    let versionNum = arr.join('') * 1
    const res: any = await getVersionAPI({
      type: type
    })
  console.log(res.msg, version, versionNum)
  if (res.msg === 'success') {
    console.log(res.result, version, versionNum)
    if (res.result.v > versionNum) {
      if(res.result.force){
        checkUpdate(res.result)
      }
    }
  }
}
const showUpdate = ref(false)
const checkUpdate = (data:any) => {
  showUpdate.value = true
  console.log(data,9999)
}
/**
 * 关闭更新
 */
const closeUpdateFlag = () => {
  showUpdate.value = false
}
</script>


<template>
  <div v-if="showUpdate==true" class="maskBox">
    <update :force="true" @close="closeUpdateFlag"></update>
  </div>
  <div class="mainBox">
    <div class="login-container">
      <div class="loginTitle"></div>
      <div class="formBox">
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" label-width="0px"
                 @submit.prevent="handleLogin">
          <el-form-item label="" prop="clientName">
            <el-input v-model="loginForm.clientName" placeholder="请输入账号"/>
          </el-form-item>
          <el-form-item label="" prop="password">
            <el-input
              v-model="loginForm.password"
              placeholder="请输入密码"
              :type="showPassword? 'text' : 'password'"
            >
              <template #suffix >
                <div @click="showPassword = !showPassword" style="display: flex;align-items: center;justify-content: center">
                  <img src="../../assets/images/show.svg" alt="" v-if="showPassword==true">
                  <img src="../../assets/images/hide.svg" alt="" v-else>
                </div>
              </template>
            </el-input>
          </el-form-item>
          <baseBtn :length="252" text="登录" @click="handleLogin"></baseBtn>
        </el-form>
      </div>
      <div class="forget" @click="forget">
        忘记密码
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.maskBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.mainBox {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: url("../../assets/images/loginBg.png");
  background-size: cover;

  .login-container {
    width: 300px;
    height: 330px;
    background: #FFFFFF;
    box-shadow: 0 4px 10px 0 var(--usr-shadow)!important;
    border-radius: 6px;

    .loginTitle {
      width: 299.68px;
      height: 100px;
      background: url("../../assets/images/loginTop.png");
      background-size: cover;
    }

    .formBox {
      width: 252px;
      margin: 0 auto;

      .full {
        width: 100%;
      }
    }
    .forget {
      margin-top: 17px;
      font-family: PingFangHK-Medium;
      font-weight: 500;
      font-size: 13px;
      color:var(--usr-forget)!important;
      letter-spacing: 0;
      text-align: center;
      line-height: 20px;
      cursor: pointer;
    }
  }
}

/* 修改输入框样式 */
:deep(.el-input__inner) {
  border-radius: 0;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: var(--usr-input-text)!important;
}
/* 修改输入框placeholder的文字颜色 */
:deep(.el-input__inner::placeholder) {
  color: var(--usr-input-placeholder)!important;
}
// 调整密码框图标位置
:deep(.el-input__wrapper) {
  border-radius:4px;
  border: 1px solid var(--usr-input-border);
  background: var(--usr-white);
  box-shadow: none;
}

// 调整密码框图标位置
:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
}

</style>
