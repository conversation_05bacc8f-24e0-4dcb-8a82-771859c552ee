#!/bin/bash

# macOS 权限检查脚本
# 用于检查LaunchDaemon安装所需的权限和系统状态

set -e

# 配置变量
DAEMON_NAME=usr.sdw
DAEMON_PLIST_PATH=/Library/LaunchDaemons/usr.sdw.plist
SERVICE_BINARY=/usr/local/bin/sdw-service
WIREGUARD_BINARY=/usr/local/bin/wireguard-go

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为macOS系统
check_macos() {
    if [[ "$(uname)" != "Darwin" ]]; then
        log_error "此脚本仅适用于macOS系统"
        exit 1
    fi
    log_success "运行在macOS系统上"
}

# 检查macOS版本
check_macos_version() {
    local version=$(sw_vers -productVersion)
    local major_version=$(echo "$version" | cut -d. -f1)
    local minor_version=$(echo "$version" | cut -d. -f2)
    
    log_info "macOS版本: $version"
    
    # 检查是否支持的版本 (macOS 10.13+)
    if [[ $major_version -ge 11 ]] || [[ $major_version -eq 10 && $minor_version -ge 13 ]]; then
        log_success "macOS版本支持LaunchDaemon"
    else
        log_error "macOS版本过低，需要10.13或更高版本"
        exit 1
    fi
}

# 检查当前用户权限
check_user_permissions() {
    log_info "检查用户权限..."
    
    if [[ $EUID -eq 0 ]]; then
        log_success "当前以root权限运行"
    else
        log_warn "当前未以root权限运行"
        log_info "LaunchDaemon安装需要管理员权限"
        log_info "请使用: sudo $0"
    fi
    
    # 检查sudo权限
    if sudo -n true 2>/dev/null; then
        log_success "具有sudo权限"
    else
        log_warn "需要sudo权限进行安装"
    fi
}

# 检查系统完整性保护(SIP)状态
check_sip_status() {
    log_info "检查系统完整性保护(SIP)状态..."
    
    local sip_status=$(csrutil status 2>/dev/null || echo "unknown")
    
    if echo "$sip_status" | grep -q "disabled"; then
        log_warn "SIP已禁用 - 这可能存在安全风险"
    elif echo "$sip_status" | grep -q "enabled"; then
        log_success "SIP已启用 - 系统安全"
    else
        log_warn "无法确定SIP状态: $sip_status"
    fi
}

# 检查现有LaunchDaemon状态
check_existing_daemon() {
    log_info "检查现有LaunchDaemon状态..."
    
    # 检查是否已安装
    if [[ -f "$DAEMON_PLIST_PATH" ]]; then
        log_warn "LaunchDaemon已存在: $DAEMON_PLIST_PATH"
        
        # 检查是否正在运行
        if launchctl list | grep -q "$DAEMON_NAME"; then
            log_warn "LaunchDaemon正在运行: $DAEMON_NAME"
            
            # 获取PID
            local pid=$(launchctl list | grep "$DAEMON_NAME" | awk '{print $1}')
            if [[ "$pid" != "-" ]]; then
                log_info "进程ID: $pid"
            fi
        else
            log_info "LaunchDaemon已安装但未运行"
        fi
    else
        log_success "未发现现有LaunchDaemon"
    fi
}

# 检查二进制文件
check_binaries() {
    log_info "检查二进制文件..."
    
    # 检查服务二进制文件
    if [[ -f "$SERVICE_BINARY" ]]; then
        log_success "服务二进制文件存在: $SERVICE_BINARY"
        
        # 检查权限
        if [[ -x "$SERVICE_BINARY" ]]; then
            log_success "服务二进制文件可执行"
        else
            log_error "服务二进制文件不可执行"
        fi
    else
        log_warn "服务二进制文件不存在: $SERVICE_BINARY"
    fi
    
    # 检查WireGuard二进制文件
    if [[ -f "$WIREGUARD_BINARY" ]]; then
        log_success "WireGuard二进制文件存在: $WIREGUARD_BINARY"
        
        # 检查权限
        if [[ -x "$WIREGUARD_BINARY" ]]; then
            log_success "WireGuard二进制文件可执行"
        else
            log_error "WireGuard二进制文件不可执行"
        fi
    else
        log_warn "WireGuard二进制文件不存在: $WIREGUARD_BINARY"
    fi
}

# 检查网络权限
check_network_permissions() {
    log_info "检查网络权限..."
    
    # 检查是否可以绑定特权端口
    if [[ $EUID -eq 0 ]]; then
        log_success "具有绑定特权端口的权限"
    else
        log_warn "可能无法绑定特权端口(需要root权限)"
    fi
    
    # 检查防火墙状态
    local firewall_status=$(sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate 2>/dev/null || echo "unknown")
    log_info "防火墙状态: $firewall_status"
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    local available_space=$(df -h / | awk 'NR==2 {print $4}')
    log_info "根目录可用空间: $available_space"
    
    # 检查/usr/local/bin目录
    if [[ -d "/usr/local/bin" ]]; then
        log_success "/usr/local/bin目录存在"
    else
        log_warn "/usr/local/bin目录不存在，将在安装时创建"
    fi
    
    # 检查/var/log目录权限
    if [[ -w "/var/log" ]]; then
        log_success "/var/log目录可写"
    else
        log_error "/var/log目录不可写"
    fi
}

# 检查依赖项
check_dependencies() {
    log_info "检查系统依赖项..."
    
    # 检查launchctl命令
    if command -v launchctl >/dev/null 2>&1; then
        log_success "launchctl命令可用"
    else
        log_error "launchctl命令不可用"
    fi
    
    # 检查pkgutil命令
    if command -v pkgutil >/dev/null 2>&1; then
        log_success "pkgutil命令可用"
    else
        log_error "pkgutil命令不可用"
    fi
}

# 生成安装建议
generate_recommendations() {
    log_info "生成安装建议..."
    
    echo ""
    echo "=== 安装建议 ==="
    
    if [[ $EUID -ne 0 ]]; then
        echo "1. 使用管理员权限运行安装脚本:"
        echo "   sudo ./postinstall"
    fi
    
    if [[ -f "$DAEMON_PLIST_PATH" ]]; then
        echo "2. 如需重新安装，请先卸载现有版本:"
        echo "   sudo ./uninstall.sh"
    fi
    
    echo "3. 安装完成后，可使用以下命令检查服务状态:"
    echo "   sudo launchctl list | grep $DAEMON_NAME"
    
    echo "4. 查看服务日志:"
    echo "   tail -f /var/log/usr.sdw.out.log"
    echo "   tail -f /var/log/usr.sdw.err.log"
    
    echo ""
}

# 主检查流程
main() {
    echo "=== macOS LaunchDaemon 权限检查 ==="
    echo ""
    
    check_macos
    check_macos_version
    check_user_permissions
    check_sip_status
    check_existing_daemon
    check_binaries
    check_network_permissions
    check_disk_space
    check_dependencies
    
    echo ""
    log_info "权限检查完成"
    
    generate_recommendations
}

# 执行主函数
main "$@"
