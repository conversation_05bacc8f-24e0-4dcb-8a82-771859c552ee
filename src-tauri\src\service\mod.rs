pub mod config;
pub mod proto {
    tonic::include_proto!("client");
}
pub mod utils;
#[cfg(windows)]
pub mod windows;

use std::{
    collections::HashMap,
    net::{IpAddr, Ipv4Addr, SocketAddr},
    pin::Pin,
    time::{Duration, SystemTime, UNIX_EPOCH},
};

#[cfg(not(target_os = "macos"))]
use defguard_wireguard_rs::Kernel;
#[cfg(target_os = "macos")]
use defguard_wireguard_rs::Userspace;
use defguard_wireguard_rs::{
    error::WireguardInterfaceError,
    host::{Host, Peer},
    key::Key,
    InterfaceConfiguration, WGApi, WireguardInterfaceApi,
};
use proto::{
    desktop_daemon_service_server::{DesktopDaemonService, DesktopDaemonServiceServer},
    CreateInterfaceRequest, InterfaceData, ReadInterfaceDataRequest, RemoveInterfaceRequest,ClearInterfaeRequest,
};
use thiserror::Error;
use tokio::{sync::mpsc, time::interval};
use tonic::{
    codegen::tokio_stream::{wrappers::ReceiverStream, Stream},
    transport::Server,
    Code, Response, Status,
};
use tracing::{debug, error, info, info_span, Instrument};

use self::config::Config;
use super::VERSION;

use std::sync::{Arc,Mutex};

const DAEMON_HTTP_PORT: u16 = 54128;
pub(super) const DAEMON_BASE_URL: &str = "http://localhost:54128";

#[derive(Error, Debug)]
pub enum DaemonError {
    #[error(transparent)]
    WireguardError(#[from] WireguardInterfaceError),
    #[error("Unexpected error: {0}")]
    Unexpected(String),
    #[error(transparent)]
    TransportError(#[from] tonic::transport::Error),
}

#[derive(Debug, Default)]
pub struct DaemonService {
    stats_period: Duration,
    // 新增连接状态存储
    connections: Arc<Mutex<HashMap<String, String>>>, // <ifname, endpoint>
}

impl DaemonService {
    #[must_use]
    pub fn new(config: &Config) -> Self {
        Self {
            stats_period: Duration::from_secs(config.stats_period),
            connections: Arc::new(Mutex::new(HashMap::new())), // 初始化连接列表
        }
    }
}

type InterfaceDataStream = Pin<Box<dyn Stream<Item = Result<InterfaceData, Status>> + Send>>;

#[cfg(not(target_os = "macos"))]
pub fn setup_wgapi(ifname: &str) -> Result<WGApi<Kernel>, Status> {
    let wgapi = WGApi::<Kernel>::new(ifname.to_string()).map_err(|err| {
        let msg = format!("Failed to setup kernel WireGuard API for interface {ifname}: {err}");
        error!("{msg}");
        Status::new(Code::Internal, msg)
    })?;

    Ok(wgapi)
}

#[cfg(target_os = "macos")]
pub fn setup_wgapi(ifname: &str) -> Result<WGApi<Userspace>, Status> {
    let wgapi = WGApi::<Userspace>::new(ifname.to_string()).map_err(|err| {
        let msg = format!("Failed to setup userspace WireGuard API for interface {ifname}: {err}");
        error!("{msg}");
        Status::new(Code::Internal, msg)
    })?;

    Ok(wgapi)
}

#[tonic::async_trait]
impl DesktopDaemonService for DaemonService {
    async fn create_interface(
        &self,
        request: tonic::Request<CreateInterfaceRequest>,
    ) -> Result<Response<()>, Status> {
        debug!("Received a request to create a new interface");
        let request = request.into_inner();
        let config: InterfaceConfiguration = request
            .config
            .ok_or(Status::new(
                Code::InvalidArgument,
                "Missing interface config in request",
            ))?
            .into();
        let ifname = &config.name;
        let endpoint = &config.peers[0].endpoint;
        let _span = info_span!("create_interface", interface_name = &ifname).entered();
        // setup WireGuard API
        let wgapi = setup_wgapi(ifname)?;

        #[cfg(not(windows))]
        {
            // create new interface
            debug!("Creating new interface {ifname}");
            wgapi.create_interface().map_err(|err| {
                let msg = format!("Failed to create WireGuard interface {ifname}: {err}");
                error!("{msg}");
                Status::new(Code::Internal, msg)
            })?;
            debug!("Done creating a new interface {ifname}");
        }

        // The WireGuard DNS config value can be a list of IP addresses and domain names, which will
        // be used as DNS servers and search domains respectively.
        debug!("Preparing DNS configuration for interface {ifname}");
        let dns_string = request.dns.unwrap_or_default();
        let dns_entries = dns_string.split(',').map(str::trim).collect::<Vec<&str>>();
        // We assume that every entry that can't be parsed as an IP address is a domain name.
        let mut dns = Vec::new();
        let mut search_domains = Vec::new();
        for entry in dns_entries {
            if let Ok(ip) = entry.parse::<IpAddr>() {
                dns.push(ip);
            } else {
                search_domains.push(entry);
            }
        }
        debug!(
            "DNS configuration for interface {ifname}: DNS: {dns:?}, Search domains: \
            {search_domains:?}"
        );

        #[cfg(not(windows))]
        let configure_interface_result = wgapi.configure_interface(&config);
        #[cfg(windows)]
        let configure_interface_result = wgapi.configure_interface(&config, &dns, &search_domains);

        configure_interface_result.map_err(|err| {
            let msg = format!("Failed to configure WireGuard interface {ifname}: {err}");
            error!("{msg}");
            Status::new(Code::Internal, msg)
        })?;

        #[cfg(not(windows))]
        {
            debug!("Configuring interface {ifname} routing");
            wgapi.configure_peer_routing(&config.peers).map_err(|err| {
                let msg =
                    format!("Failed to configure routing for WireGuard interface {ifname}: {err}");
                error!("{msg}");
                Status::new(Code::Internal, msg)
            })?;

            if dns.is_empty() {
                debug!(
                    "No DNS configuration provided for interface {ifname}, skipping DNS \
                    configuration"
                );
            } else {
                debug!(
                    "The following DNS servers will be set: {dns:?}, search domains: \
                    {search_domains:?}"
                );
                wgapi.configure_dns(&dns, &search_domains).map_err(|err| {
                    let msg =
                        format!("Failed to configure DNS for WireGuard interface {ifname}: {err}");
                    error!("{msg}");
                    Status::new(Code::Internal, msg)
                })?;
            }
        }
        // 添加 endpoint 存在性检查
        let endpoint_addr = endpoint.as_ref().ok_or_else(|| {
            Status::new(
                Code::InvalidArgument,
                format!("Peer endpoint not found for interface {ifname}"),
            )
        })?;
        let endpoint_str = format!("{}:{}", endpoint_addr.ip(), endpoint_addr.port());
        // 在接口创建成功后记录连接信息
        self.connections.lock().unwrap().insert(
            ifname.to_string(), 
            endpoint_str
        );
    
        debug!("Finished creating a new interface {ifname}");
        Ok(Response::new(()))
    }

    async fn remove_interface(
        &self,
        request: tonic::Request<RemoveInterfaceRequest>,
    ) -> Result<Response<()>, Status> {
        debug!("Received a request to remove an interface");
        let request = request.into_inner();
        let ifname = request.interface_name;
        let _span = info_span!("remove_interface", interface_name = &ifname).entered();
        debug!("Removing interface {ifname}");

        let wgapi = setup_wgapi(&ifname)?;

        #[cfg(not(windows))]
        {
            debug!("Cleaning up interface {ifname} routing");
            // Ignore error as this should not be considered fatal,
            // e.g. endpoint might fail to resolve DNS name.
            if let Err(err) = wgapi.remove_endpoint_routing(&request.endpoint) {
                error!(
                    "Failed to remove routing for endpoint {}: {err}",
                    request.endpoint
                );
            }
        }

        wgapi.remove_interface().map_err(|err| {
            let msg = format!("Failed to remove WireGuard interface {ifname}: {err}");
            error!("{msg}");
            Status::new(Code::Internal, msg)
        })?;
    
        debug!("Finished removing interface {ifname}");
         
        // 移除连接记录
        self.connections.lock().unwrap().remove(&ifname);
        Ok(Response::new(()))
    }

    type ReadInterfaceDataStream = InterfaceDataStream;

    async fn read_interface_data(
        &self,
        request: tonic::Request<ReadInterfaceDataRequest>,
    ) -> Result<Response<Self::ReadInterfaceDataStream>, Status> {
        let request = request.into_inner();
        let ifname = request.interface_name;
        debug!(
            "Received a request to start a new network usage stats data stream for interface \
            {ifname}"
        );
        let span = info_span!("read_interface_data", interface_name = &ifname);

        // Setup WireGuard API.
        let wgapi = setup_wgapi(&ifname)?;
        let mut interval = interval(self.stats_period);
        let (tx, rx) = mpsc::channel(64);

        span.in_scope(|| {
            info!("Spawning statistics collector task for interface {ifname}");
        });

        tokio::spawn(
            async move {
                // Helper map to track if peer data is actually changing to avoid sending duplicate
                // stats.
                let mut peer_map = HashMap::new();

                loop {
                    // Loop delay
                    interval.tick().await;
                    debug!(
                    "Gathering network usage statistics for client's network activity on {ifname}");
                    match wgapi.read_interface_data() {
                        Ok(mut host) => {
                            let peers = &mut host.peers;
                            debug!(
                                "Found {} peers configured on WireGuard interface",
                                peers.len()
                            );
                            // Filter out never connected peers.
                            peers.retain(|_, peer| {
                                // Last handshake time-stamp must exist...
                                if let Some(last_hs) = peer.last_handshake {
                                    // ...and not be UNIX epoch.
                                    if last_hs != SystemTime::UNIX_EPOCH
                                        && match peer_map.get(&peer.public_key) {
                                            Some(last_peer) => last_peer != peer,
                                            None => true,
                                        }
                                    {
                                        debug!(
                                            "Peer {} statistics changed; keeping it.",
                                            peer.public_key
                                        );
                                        peer_map.insert(peer.public_key.clone(), peer.clone());
                                        return true;
                                    }
                                }
                                debug!(
                                    "Peer {} statistics didn't change; ignoring it.",
                                    peer.public_key
                                );
                                false
                            });
                            if let Err(err) = tx.send(Ok(host.into())).await {
                                error!(
                                    "Couldn't send network usage stats update for {ifname}: {err}"
                                );
                                break;
                            }
                        }
                        Err(err) => {
                            error!(
                                "Failed to retrieve network usage stats for interface {ifname}: \
                                {err}"
                            );
                            break;
                        }
                    }
                    debug!("Network activity statistics for interface {ifname} sent to the client");
                }
                debug!(
                    "The client has disconnected from the network usage statistics data stream \
                for interface {ifname}, stopping the statistics data collection task."
                );
            }
            .instrument(span),
        );

        let output_stream = ReceiverStream::new(rx);
        Ok(Response::new(
            Box::pin(output_stream) as Self::ReadInterfaceDataStream
        ))
    }

    async fn clear_interfae(
        &self,
        request: tonic::Request<ClearInterfaeRequest>,
    ) -> Result<Response<()>, Status> {
        let request = request.into_inner();
        //循环获取connections 执行removeInterface
        // 添加更详细的日志输出
        debug!("开始获取连接锁");
        // 提前获取接口列表并立即释放锁
        let ifnames: Vec<String> = {
            let connections = self.connections.lock().unwrap();
            connections.keys().cloned().collect()
        };
        debug!("成功获取{}个连接", ifnames.len());
        
        if ifnames.is_empty() {
            debug!("无待清理接口");
            return Ok(Response::new(()));
        }
    
        for name in ifnames {
            let ifname = name.clone();
            debug!("开始处理接口 {}", ifname); // 新增阶段日志   
            debug!("初始化WireGuard API");
            let wgapi = match setup_wgapi(&ifname) {
                Ok(api) => api,
                Err(e) => {
                    error!("接口{}初始化失败: {}", ifname, e);
                    continue; // 改为继续处理其他接口
                }
            };
    
            #[cfg(not(windows))]
            {
                debug!("清理{}的路由配置", ifname);
                let endpoint = self.connections.lock().unwrap().get(&ifname).cloned().unwrap_or_default();
                // Ignore error as this should not be considered fatal,
                // e.g. endpoint might fail to resolve DNS name.        
                if let Err(err) = wgapi.remove_endpoint_routing(&endpoint) {
                    error!(
                        "Failed to clear routing for endpoint {}: {err}",
                        endpoint
                    );
                }
            }
             // 改为错误捕获而非立即返回
            match wgapi.remove_interface() {
                Ok(_) => info!("成功移除接口 {ifname}"),
                Err(err) => {
                    let msg = format!("移除接口 {ifname} 失败: {err}");
                    error!("{msg}");
                    // 继续处理其他接口而非返回错误
                    continue;
                }
            };
        }
        // 清空连接记录
        self.connections.lock().unwrap().clear();
        debug!("Finished clearing interface");
        Ok(Response::new(()))  
    }
}

pub async fn run_server(config: Config) -> anyhow::Result<()> {
    debug!("SDW Daemon Process ID: {}", std::process::id()); // 新增进程ID日志
    let addr = SocketAddr::new(IpAddr::V4(Ipv4Addr::LOCALHOST), DAEMON_HTTP_PORT);
    let daemon_service = DaemonService::new(&config);

    info!(
        "sdw daemon version {} started, listening on {addr}",
        VERSION
    );
    debug!("sdw daemon configuration: {config:?}");

    Server::builder()
        .trace_fn(|_| tracing::info_span!("sdw"))
        .add_service(DesktopDaemonServiceServer::new(daemon_service))
        .serve(addr)
        .await?;

    Ok(())
}

impl From<InterfaceConfiguration> for proto::InterfaceConfig {
    fn from(config: InterfaceConfiguration) -> Self {
        Self {
            name: config.name,
            prvkey: config.prvkey,
            address: config
                .addresses
                .first()
                .map(|addr| addr.to_string())
                .unwrap_or_default(),
            port: config.port,
            peers: config.peers.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<proto::InterfaceConfig> for InterfaceConfiguration {
    fn from(config: proto::InterfaceConfig) -> Self {
        let mut addresses = Vec::new();
        if let Ok(address) = config.address.parse() {
            addresses.push(address);
        }
        Self {
            name: config.name,
            prvkey: config.prvkey,
            addresses,
            port: config.port,
            peers: config.peers.into_iter().map(Into::into).collect(),
            mtu: None,
        }
    }
}

impl From<Peer> for proto::Peer {
    fn from(peer: Peer) -> Self {
        Self {
            public_key: peer.public_key.to_lower_hex(),
            preshared_key: peer.preshared_key.map(|key| key.to_lower_hex()),
            protocol_version: peer.protocol_version,
            endpoint: peer.endpoint.map(|addr| addr.to_string()),
            last_handshake: peer.last_handshake.map(|time| {
                time.duration_since(UNIX_EPOCH)
                    .expect("Time went backwards")
                    .as_secs()
            }),
            tx_bytes: peer.tx_bytes,
            rx_bytes: peer.rx_bytes,
            persistent_keepalive_interval: peer.persistent_keepalive_interval.map(u32::from),
            allowed_ips: peer
                .allowed_ips
                .into_iter()
                .map(|addr| addr.to_string())
                .collect(),
        }
    }
}

impl From<proto::Peer> for Peer {
    fn from(peer: proto::Peer) -> Self {
        Self {
            public_key: Key::decode(peer.public_key).expect("Failed to parse public key"),
            preshared_key: peer
                .preshared_key
                .map(|key| Key::decode(key).expect("Failed to parse preshared key: {key}")),
            protocol_version: peer.protocol_version,
            endpoint: peer.endpoint.map(|addr| {
                addr.parse()
                    .expect("Failed to parse endpoint address: {addr}")
            }),
            last_handshake: peer
                .last_handshake
                .map(|timestamp| UNIX_EPOCH + Duration::from_secs(timestamp)),
            tx_bytes: peer.tx_bytes,
            rx_bytes: peer.rx_bytes,
            persistent_keepalive_interval: peer
                .persistent_keepalive_interval
                .and_then(|interval| u16::try_from(interval).ok()),
            allowed_ips: peer
                .allowed_ips
                .into_iter()
                .map(|addr| addr.parse().expect("Failed to parse allowed IP: {addr}"))
                .collect(),
        }
    }
}

impl From<Host> for InterfaceData {
    fn from(host: Host) -> Self {
        Self {
            listen_port: u32::from(host.listen_port),
            peers: host.peers.into_values().map(Into::into).collect(),
        }
    }
}

#[cfg(test)]
mod tests {
    // use std::{str::FromStr, time::SystemTime};

    // use defguard_wireguard_rs::{key::Key, net::IpAddrMask};
    // use x25519_dalek::{EphemeralSecret, PublicKey};

    // use super::*;

    // #[test]
    // fn convert_peer() {
    //     let secret = EphemeralSecret::random();
    //     let key = PublicKey::from(&secret);
    //     let peer_key: Key = key.as_ref().try_into().unwrap();
    //     let mut base_peer = Peer::new(peer_key);
    //     let addr = IpAddrMask::from_str("**********/32").unwrap();
    //     base_peer.allowed_ips.push(addr);
    //     // Workaround since nanoseconds are lost in conversion.
    //     base_peer.last_handshake = Some(SystemTime::UNIX_EPOCH);
    //     base_peer.protocol_version = Some(3);
    //     base_peer.endpoint = Some("127.0.0.1:8080".parse().unwrap());
    //     base_peer.tx_bytes = 100;
    //     base_peer.rx_bytes = 200;

    //     let proto_peer: proto::Peer = base_peer.clone().into();

    //     let converted_peer: Peer = proto_peer.into();

    //     assert_eq!(base_peer, converted_peer);
    // }
}
