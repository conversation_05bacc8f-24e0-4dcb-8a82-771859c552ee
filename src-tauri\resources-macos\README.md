# macOS LaunchDaemon 安装指南

本文档说明如何在macOS系统上安装和管理有人异地组网的LaunchDaemon服务。

## 概述

LaunchDaemon是macOS系统级服务，运行在系统启动时，具有root权限，用于管理WireGuard网络接口。

## 系统要求

- macOS 10.13 (High Sierra) 或更高版本
- 管理员权限
- 至少100MB可用磁盘空间

## 文件结构

```
resources-macos/
├── binaries/                          # 二进制文件
│   ├── wireguard-go-aarch64-apple-darwin  # Apple Silicon版本
│   └── wireguard-go-x86_64-apple-darwin   # Intel版本
├── resources/                         # 资源文件
│   ├── usr.sdw.plist                 # LaunchDaemon配置文件
│   └── uninstall.sh                  # 卸载脚本
└── scripts/                          # 安装脚本
    ├── postinstall                   # 安装后脚本
    └── check-permissions.sh          # 权限检查脚本
```

## 安装过程

### 1. 权限检查

在安装前，建议运行权限检查脚本：

```bash
sudo ./resources-macos/scripts/check-permissions.sh
```

### 2. 自动安装

通过应用程序包安装时，`postinstall`脚本会自动执行以下操作：

1. **创建目录结构**
   - `/usr/local/bin/` - 二进制文件目录
   - `/var/log/` - 日志文件目录

2. **安装二进制文件**
   - 创建符号链接到 `/usr/local/bin/sdw-service`
   - 创建符号链接到 `/usr/local/bin/wireguard-go`
   - 设置执行权限

3. **安装LaunchDaemon**
   - 复制 `usr.sdw.plist` 到 `/Library/LaunchDaemons/`
   - 设置正确的文件权限 (root:wheel, 644)
   - 加载并启动服务

4. **设置日志**
   - 创建日志文件 `/var/log/usr.sdw.out.log`
   - 创建错误日志 `/var/log/usr.sdw.err.log`

### 3. 手动安装

如需手动安装，请以管理员权限运行：

```bash
sudo ./resources-macos/scripts/postinstall
```

## LaunchDaemon 配置

### 配置文件位置
`/Library/LaunchDaemons/usr.sdw.plist`

### 主要配置项

- **Label**: `usr.sdw` - 服务标识符
- **Program**: `/usr/local/bin/sdw-service` - 服务程序路径
- **UserName**: `root` - 运行用户
- **KeepAlive**: 自动重启配置
- **RunAtLoad**: 系统启动时自动运行
- **StandardOutPath**: 标准输出日志路径
- **StandardErrorPath**: 错误输出日志路径

## 服务管理

### 检查服务状态

```bash
# 查看服务是否运行
sudo launchctl list | grep usr.sdw

# 查看详细状态
sudo launchctl list usr.sdw
```

### 手动控制服务

```bash
# 启动服务
sudo launchctl load /Library/LaunchDaemons/usr.sdw.plist

# 停止服务
sudo launchctl stop usr.sdw

# 卸载服务
sudo launchctl unload /Library/LaunchDaemons/usr.sdw.plist
```

### 查看日志

```bash
# 查看输出日志
tail -f /var/log/usr.sdw.out.log

# 查看错误日志
tail -f /var/log/usr.sdw.err.log

# 查看系统日志
log show --predicate 'subsystem == "usr.sdw"' --info
```

## 网络权限

LaunchDaemon需要以下网络权限：

1. **绑定端口**: 监听gRPC服务端口 (50051)
2. **网络接口管理**: 创建和配置WireGuard接口
3. **路由表修改**: 设置网络路由

## 安全考虑

### 文件权限
- LaunchDaemon plist文件: `root:wheel 644`
- 二进制文件: `root:wheel 755`
- 日志文件: `root:wheel 644`

### 网络安全
- 服务仅监听本地回环地址 (127.0.0.1)
- 使用gRPC进行进程间通信
- 支持TLS加密连接

### 系统完整性保护 (SIP)
- 兼容启用SIP的系统
- 不修改系统保护目录
- 遵循macOS安全最佳实践

## 故障排除

### 常见问题

1. **权限被拒绝**
   ```bash
   # 检查文件权限
   ls -la /Library/LaunchDaemons/usr.sdw.plist
   ls -la /usr/local/bin/sdw-service
   ```

2. **服务无法启动**
   ```bash
   # 检查配置文件语法
   plutil -lint /Library/LaunchDaemons/usr.sdw.plist
   
   # 手动测试二进制文件
   /usr/local/bin/sdw-service --help
   ```

3. **网络连接问题**
   ```bash
   # 检查端口占用
   lsof -i :50051
   
   # 检查防火墙设置
   sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate
   ```

### 日志分析

查看日志文件中的关键信息：

- **启动成功**: "sdw daemon version X.X.X started"
- **权限错误**: "Permission denied"
- **网络错误**: "bind: address already in use"
- **配置错误**: "Failed to parse config"

## 卸载

### 自动卸载

运行卸载脚本：

```bash
sudo ./resources-macos/resources/uninstall.sh
```

### 手动卸载

1. 停止并卸载服务：
   ```bash
   sudo launchctl stop usr.sdw
   sudo launchctl unload /Library/LaunchDaemons/usr.sdw.plist
   ```

2. 删除文件：
   ```bash
   sudo rm -f /Library/LaunchDaemons/usr.sdw.plist
   sudo rm -f /usr/local/bin/sdw-service
   sudo rm -f /usr/local/bin/wireguard-go
   sudo rm -f /var/log/usr.sdw.*.log
   ```

3. 删除应用程序：
   ```bash
   sudo rm -rf "/Applications/有人异地组网.app"
   ```

4. 清理包记录：
   ```bash
   sudo pkgutil --forget usr.sdw
   ```

## 开发和调试

### 本地测试

```bash
# 编译服务
cargo build --release --bin sdw-service

# 手动运行服务
sudo ./target/release/sdw-service --log-level debug

# 测试gRPC连接
grpcurl -plaintext localhost:50051 list
```

### 配置修改

修改LaunchDaemon配置后需要重新加载：

```bash
sudo launchctl unload /Library/LaunchDaemons/usr.sdw.plist
sudo launchctl load /Library/LaunchDaemons/usr.sdw.plist
```

## 支持

如遇到问题，请提供以下信息：

1. macOS版本: `sw_vers`
2. 服务状态: `sudo launchctl list usr.sdw`
3. 日志文件: `/var/log/usr.sdw.*.log`
4. 系统日志: `log show --predicate 'subsystem == "usr.sdw"' --last 1h`

---

**注意**: 本服务需要管理员权限运行，请确保从可信来源安装，并定期检查系统安全状态。
