<script lang="ts" setup>
import {useRoute, useRouter} from "vue-router";
import {computed, onMounted, ref, watch} from "vue";
import {getVersionAPI, logoutAPI} from "../../api/UserApi.ts";
import update from '../../components/layout/update.vue'
import account from '../../components/layout/account.vue'
import {invoke} from "@tauri-apps/api/tauri";
import {listen} from "@tauri-apps/api/event";
import {ElMessage} from "element-plus";
import {useMqttStore, useN3nStore, useTokenStore} from '../../store'
const n3nStore: any = useN3nStore()
const tokenStore = useTokenStore()
const route = useRoute()
const router = useRouter()
watch(
  () => route.path,
  (newPath) => {
    if (newPath) {
      getVersion()
    }
  }
);

const mqttStore = useMqttStore()
const activeIndex = computed(() => {
  return route.meta.menuIndex
})
/**
 * 跳转页面
 */
const toPage = (index: string) => {
  router.push({path: index})
}
/**
 * 退出登录
 */
const exitPage = () => {
  disableTunnel()
}

/**
 * 关闭组网（二层/三层）
 */
async function disableTunnel(): Promise<void> {
  console.log('禁用', mqttStore.wgConfig)
  try {
    await invoke('disable_wiresock')
  } catch (error) {
    console.error('禁用失败:', error)
  }
  if (mqttStore.wgConfig.level == 2) {
    try {
      let resStop = await invoke('stop_vpn')
      console.log(resStop, 'result')
      // 清除网关
      let result = await invoke('reset_network', {
        adapter: n3nStore.network.name,
      })
      // await invoke('stop_monitor')
      console.log(result, 'result')
    } catch (error) {
      console.error('禁用失败:', error)
    }
  }
  mqttStore.setChartData([])
  mqttStore.setWgConfig()
  mqttStore.disconnect()
  let res: any = await logoutAPI()
  if (res.msg === 'success') {
    tokenStore.exitToken()
    router.push({path: '/login'})
  }
}

// const toSpeed = () => {
//   toPage('/speed')
// }
/**
 * 个人信息
 */
const toUserInfo = () => {
  toPage('/userInfo')
}
const showUpdate = ref(false)
/**
 * 更新软件
 */
const checkUpdate = () => {
  if (shouldUpdate.value == true) {
    showUpdate.value = true
  } else {
    ElMessage.success(currentVersion.value + '已经是最新版本')
  }
}
const showAccountFlag = ref(false)
/**
 * 切换账号
 */
const changeAccount = () => {
  showAccountFlag.value = true
}
const showDropdown = ref(false);
/**
 * 隐藏悬浮窗
 */
const startHideTimer = () => {
  showDropdown.value = false;
};
const shouldUpdate = ref(false)
const force: any = ref(false)
const currentVersion = ref('')
/**
 * 获取版本
 */
const getVersion = async () => {
  const version: any = await invoke('get_version')
  const osType = await invoke<string>('get_os_type');
  //判断osType 根据值设置type windows == 1 mac ==2
  let type = 1 //默认windows
  if (osType === 'macos-aarch64') {
    type = 2
  }
  if (osType === 'macos-x86_64') {
    type = 3
  }
  if (osType === 'linux'){
    type = 4
  }
  currentVersion.value = version
  //version 1.0.14  转换为10014
  const arr = version.split('.')
  if (arr[1] < 10) {
    arr[1] = '0' + arr[1]
  }
  if (arr[2] < 10) {
    arr[2] = '0' + arr[1]
  }
  console.log("type :", type)
  let versionNum = arr.join('') * 1
  const res: any = await getVersionAPI({
    type: type
  })
  console.log(res.msg, version, versionNum)
  if (res.msg === 'success') {
    console.log(res.result, version, versionNum)
    if (res.result.v > versionNum) {
      shouldUpdate.value = true
      force.value = res.result.force
      if (res.result.force) {
        checkUpdate()
      }
    }
  }
}
/**
 * 关闭更新
 */
const closeUpdateFlag = () => {
  showUpdate.value = false
}
/**
 * 关闭切换账号
 */
const closeAccount = () => {
  showAccountFlag.value = true
}
onMounted(() => {
  getVersion()
  // todo  调试使用，打印rust的报错
  listen('debug', (event) => {
    const payload: any = event.payload;
    console.log(payload, 'debug')
  });
})
const closeUpdate = () => {
  if (force.value == true) {
    // 不做处理
    showUpdate.value = true
  } else {
    showUpdate.value = false
  }
}
</script>

<template>
  <div class="menu">
    <!--    首页     -->
    <div :class="['menuItem',activeIndex=='homePage'?'active':'']" @click="toPage('/home')">
      <img v-if="activeIndex=='homePage'" alt="" src="../../assets/images/homeActive.svg">
      <img v-else alt="" src="../../assets/images/home.svg">
    </div>
    <!--    终端    -->
    <div :class="['menuItem',activeIndex=='listPage'?'active':'']" @click="toPage('/list')">
      <img v-if="activeIndex=='listPage'" alt="" src="../../assets/images/networkActive.svg">
      <img v-else alt="" src="../../assets/images/network.svg">
    </div>
    <div style="flex:1"></div>
    <!--    设置    -->
    <div class="dropdown-container">
      <div
        :class="['menuItem',showDropdown == true?'active':'']"
        @click.stop="showDropdown = true"
      >
        <img v-if="showDropdown == true" alt="" src="../../assets/images/settingActive.svg">
        <img v-else alt="" src="../../assets/images/setting.svg">
      </div>
    </div>
    <div v-if="showDropdown" class="maskBoxA" @click.prevent.stop="startHideTimer">
      <div
        class="dropdown-menu"
      >
        <!--        <div class="dropdown-item" @click="toSpeed">测速</div>-->
        <div class="dropdown-item" @click="toUserInfo">个人信息</div>
        <div :class="[shouldUpdate?'dropdown-item dropdown-item-update':'dropdown-item']"
             @click.stop.prevent="checkUpdate">
          检测更新
        </div>
        <div class="dropdown-item" @click="changeAccount">切换账号</div>
        <div class="dropdown-item" @click="exitPage">退出登录</div>
      </div>
    </div>
    <div v-if="showUpdate==true" class="maskBox" @click.prevent.stop="closeUpdate">
      <update :force="force" @close="closeUpdateFlag"></update>
    </div>
    <div v-if="showAccountFlag" class="maskBox" @click.prevent.stop="showAccountFlag=false">
      <account @close="closeAccount"></account>
    </div>
  </div>
</template>

<style scoped>
.menu {
  width: 54px;
  height: 100%;
  color: #000;
  display: flex;
  flex-direction: column;
  padding-left: 16px;

  .menuItem {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .active {
    background: var(--usr-bar-bg);
  }
}

.dropdown-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 4px;
  margin-bottom: 32px;

  .menuItem {
    margin: 0;
  }
}

.maskBoxA {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.dropdown-menu {
  position: absolute;
  bottom: 32px;
  left: 67px; /* 根据容器宽度调整 */
  width: 112px;
  background: var(--usr-white);
  box-shadow: 0 4px 10px 0 var(--usr-shadow);
  z-index: 9999;
  border-radius: 6px;
  padding: 4px;
}

.dropdown-item {
  width: 112px;
  height: 35px;
  position: relative;
  font-family: PingFangHK-Regular;
  font-weight: 400;
  font-size: 12px;
  color: var(--usr-btn-border-text);
  letter-spacing: 0;
  text-align: center;
  line-height: 35px;
}

.dropdown-item:hover {
  background: var(--usr-menu-hover);
}

.dropdown-item-update::before {
  position: absolute;
  content: '.';
  left: 20px;
  bottom: 14px;
  width: 6px;
  height: 6px;
  background: #F82765;
  border-radius: 6px;
}

.maskBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
</style>
