import { defineStore } from 'pinia'
import { reactive, toRefs } from 'vue'
import {aliveAPI} from "../../api/UserApi.ts";
export const useTokenStore = defineStore('token', () => {
    const initData: any = reactive({
        isLoggedIn: false,
        token:'',
        aliveTimer:null,
        refreshFlag: 0 // 用于强制触发更新的标记
    })
    // 设置store中记录的整体路由
    function loginToken(token:any) {
        initData.isLoggedIn = true
        initData.token = token
        triggerAppRefresh()
        sessionStorage.setItem('token', token)
        setTimer()
    }
    function exitToken() {
        clearInterval(initData.aliveTimer)
        initData.aliveTimer = null
        initData.isLoggedIn = false
        triggerAppRefresh()
        sessionStorage.removeItem('token')
    }
    function triggerAppRefresh() {
        initData.refreshFlag++;
    }
    async function setTimer () {
        clearInterval(initData.aliveTimer)
        initData.aliveTimer = null
        await aliveAPI()
        initData.aliveTimer = setInterval(async () => {
            if (initData.token) {
                await aliveAPI()
            }else{
                clearInterval(initData.aliveTimer)
                initData.aliveTimer = null
            }
        }, 60000)
    }
    return { ...toRefs(initData), loginToken, exitToken }
},{persist: true})
