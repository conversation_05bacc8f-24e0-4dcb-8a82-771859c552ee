<template>
  <div class="mainBox">
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import { invoke } from '@tauri-apps/api'
import {useRouter} from "vue-router";
const TAPUSR_VERSION = '9.24.7'
const router = useRouter()
const isInstalled = ref(false)
const version: any = ref('')
const props = defineProps(['loading'])
/**
 *  watch监听 TapUsr的安装状态,变成true ，就返回首页
 */
watch(() => isInstalled.value, (newValue) => {
  if (newValue) {
    props.loading.close()
    router.push('/index')
  }
}, {immediate: true})
const errorMessage: any = ref('')
const installing: any = ref(false)
onMounted(()=>{
  getTapUsrVersion()
})
/**
 *  watch监听 TapUsr的安装状态,变成true ，就返回首页
 */
async function getTapUsrVersion(): Promise<void> {
  console.log('Checking TapUsr version')
  const result: string = await invoke('get_tap_usr_version')
  if (result === 'TAP-USR-Windows_not_installed') {
    console.log('TapUsr is not installed')
    isInstalled.value = false
    handleInstallButtonClick()
    return
  }
  console.log('TapUsr installed version:', result)
  version.value = result
  if (result === TAPUSR_VERSION) {
    console.log('Supported version of TapUsr is installed')
    const adapters: any[] = await invoke('get_network_adapters_all');
    console.log('获取所有网卡', adapters);
    const targetAdapter = adapters.find(adapter => adapter.description.includes('TAP-Windows'));
    if (!targetAdapter) {
      console.log('安装网卡，未清理注册表');
      isInstalled.value = false
      handleInstallButtonClick()
    }else{
      isInstalled.value = true
    }
  } else {
    console.log('Unsupported TapUsr version installed')
    isInstalled.value = true
  }
}
/**
 *  安装TapUsr
 */
const handleInstallButtonClick =  () => {
  errorMessage.value = ''
  installing.value = true
  checkVersion()
}
/**
 *  安装TapUsr过程
 */
const checkVersion = async () => {
  console.log('Checking TapUsr version')
  try {
    const result: string = await invoke('install_tap_usr')
    const lines = JSON.parse(result)
    console.log(lines,result,9999)
    for (const line of lines) {
      switch (line) {
        case '0':
          console.log('TapUsr installed successfully')
          isInstalled.value = false
          break
        case '1602':
          console.log('User cancelled the installation')
          errorMessage.value('User cancelled the installation')
          isInstalled.value = true
          break
        case '1603':
          console.log('A newer version of TapUsr is installed')
          errorMessage.value('A newer version of TapUsr is installed')
          isInstalled.value = true
          break
        default:
          console.log(`Unknown exit code: ${line}`)
          errorMessage.value(
            'Unknown error attempting to install TapUsr. To resolve this issue, manually install TapUsr. You can find the TapUsr installer in the same directory where sdw is installed.',
          )
      }
    }
  } catch (error) {
    console.error('Error in handleInstallButtonClick: ', error)
    const errorString: string = (error as Error).toString()
    if (errorString.includes('program not found')) {
      errorMessage.value(
        'PowerShell failed to start. To resolve this issue, manually install TapUsr. You can find the TapUsr installer in the same directory where sdw is installed.',
      )
    } else {
      errorMessage.value(errorString)
    }
  } finally {
    getTapUsrVersion()
    installing.value = false
  }
}
</script>

<style scoped>
.mainBox{
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--usr-white);
}
</style>
