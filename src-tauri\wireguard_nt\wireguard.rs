#[tauri::command]
async fn enable_wiresock(
    tunnel: Tunnel,
    log_level: String,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    // Check if enable_wiresock is already running
    {
        let state = WIRESOCK_STATE.lock().unwrap();
        if state.wiresock_status != "STOPPED" {
            println!(
                "wiresock_state at start of enable_wiresock is {:?}",
                &*state
            );
            return Err("enable_wiresock is already running".into());
        }
    }

    // Update the WIRESOCK_STATE and emit the change
    update_state(&app_handle, |state| {
        state.tunnel_id = tunnel.id.clone();
        state.wiresock_status = "STARTING".to_string();
        state.tunnel_status = "DISCONNECTED".to_string();
        state.logs = Vec::new();
    });

    // Create a guard that will reset the wiresock_status when dropped
    let _guard = WiresockEnablingGuard;
        let current_dir = env::current_dir().unwrap();
        let wireguard_path = current_dir.join("wireguard_nt").join("bin").join("amd64").join("wireguard.dll");
    // Get the users home directory
    let mut tunnel_config_path = PathBuf::new();
    match home::home_dir() {
        Some(path) => tunnel_config_path.push(path),
        None => return Err("Unable to retrieve the user home directory.".into()),
    }

    // Create a path to the wiresock config directory
    tunnel_config_path.push("AppData");
    tunnel_config_path.push("Local");
    tunnel_config_path.push("sdw");

    // Create a TunnlTo directory in appdata/local if it doesn't exist already
    fs::create_dir_all(&tunnel_config_path).unwrap_or_else(|e| panic!("Error creating dir: {}", e));

    // Create a path to the wiresock config file
    tunnel_config_path.push("tunnel.conf");

    // Write the config file to disk
    let mut w = fs::File::create(&tunnel_config_path).unwrap();

    // Interface section
    writeln!(&mut w, "[Interface]").unwrap();

    // Interface Private Key
    writeln!(&mut w, "PrivateKey = {}", tunnel.interface.privateKey).unwrap();

    // Interface addresses
    let mut interface_addresses = String::new();

    if !tunnel.interface.ipv4Address.is_empty() {
        interface_addresses = format!("Address = {}", tunnel.interface.ipv4Address);
    }

    if !tunnel.interface.ipv6Address.is_empty() {
        if !interface_addresses.is_empty() {
            // Ensure there is comma between the ipv4 and ipv6 addresses
            interface_addresses =
                format!("{}, {}", interface_addresses, tunnel.interface.ipv6Address);
        } else {
            // There is no ipv4 address set, but a ipv6 address is set
            interface_addresses = format!("Address = {}", tunnel.interface.ipv6Address);
        }
    }

    // Write the interface addresses to the config file
    if !interface_addresses.is_empty() {
        writeln!(&mut w, "{}", interface_addresses).unwrap();
    }

    // Interface ListenPort
    if !tunnel.interface.port.is_empty() {
        writeln!(&mut w, "ListenPort = {}", tunnel.interface.port).unwrap();
    }

    // Interface DNS
    if !tunnel.interface.dns.is_empty() {
        writeln!(&mut w, "DNS = {}", tunnel.interface.dns).unwrap();
    }

    // Interface MTU
    if !tunnel.interface.mtu.is_empty() {
        writeln!(&mut w, "MTU = {}", tunnel.interface.mtu).unwrap();
    }

    // Put a space between the interface and peer sections for readability
    writeln!(&mut w, "").unwrap();

    // Peer section
    writeln!(&mut w, "[Peer]").unwrap();

    // Peer Public Key
    writeln!(&mut w, "PublicKey = {}", tunnel.peer.publicKey).unwrap();

    // Peer Preshared Key
    if !tunnel.peer.presharedKey.is_empty() {
        writeln!(&mut w, "PresharedKey = {}", tunnel.peer.presharedKey).unwrap();
    }

    // Peer Endpoint
    writeln!(
        &mut w,
        "Endpoint = {}:{}",
        tunnel.peer.endpoint, tunnel.peer.port
    )
    .unwrap();

    // Peer Persistent Keep-alive
    if !tunnel.peer.persistentKeepalive.is_empty() {
        writeln!(
            &mut w,
            "PersistentKeepalive = {}",
            tunnel.peer.persistentKeepalive
        )
        .unwrap();
    }

    // Rules

    // Allowed

    // Allowed Apps
    let mut allowed_apps = String::new();
    if !tunnel.rules.allowed.apps.is_empty() {
        allowed_apps = format!("AllowedApps = {}", tunnel.rules.allowed.apps);
    }

    // Allowed Folders
    if !tunnel.rules.allowed.folders.is_empty() {
        if !allowed_apps.is_empty() {
            // Ensure there is comma between the allowed apps and allowed folders
            allowed_apps = format!("{}, {}", allowed_apps, tunnel.rules.allowed.folders);
        } else {
            allowed_apps = format!("AllowedApps = {}", tunnel.rules.allowed.folders);
        }
    }

    // Write Allowed Apps/Folders to the config file
    if !allowed_apps.is_empty() {
        writeln!(&mut w, "{}", allowed_apps).unwrap();
    }

    // Allowed IP Addresses
    if !tunnel.rules.allowed.ipAddresses.is_empty() {
        writeln!(&mut w, "AllowedIPs = {}", tunnel.rules.allowed.ipAddresses).unwrap();
    }

    // Disallowed

    // Disallowed Apps
    let mut disallowed_apps = String::new();
    if !tunnel.rules.disallowed.apps.is_empty() {
        disallowed_apps = format!("DisallowedApps = {}", tunnel.rules.disallowed.apps);
    }

    // Disallowed Folders
    if !tunnel.rules.disallowed.folders.is_empty() {
        if !disallowed_apps.is_empty() {
            // Ensure there is comma between the disallowed apps and disallowed folders
            disallowed_apps = format!("{}, {}", disallowed_apps, tunnel.rules.disallowed.folders);
        } else {
            disallowed_apps = format!("DisallowedApps = {}", tunnel.rules.disallowed.folders);
        }
    }

    // Write Disallowed Apps/Folders to the config file
    if !disallowed_apps.is_empty() {
        writeln!(&mut w, "{}", disallowed_apps).unwrap();
    }

    // Disallowed IP Addresses
    if !tunnel.rules.disallowed.ipAddresses.is_empty() {
        writeln!(
            &mut w,
            "DisallowedIPs = {}",
            tunnel.rules.disallowed.ipAddresses
        )
        .unwrap();
    }

    // Build the full path to the wiresock executable
    let mut wiresock_location: String = get_wiresock_install_path().unwrap(); // unwrapping as we expect Wiresock is installed at this point
    let exe: &str = "/bin/wiresock-client.exe";
    wiresock_location.push_str(exe);

    // Create a string of the WireSock config file path
    let wiresock_config_path = &tunnel_config_path.into_os_string().into_string().unwrap();

    // Enable Wiresock and output the stdout
    let mut child = Command::new(wiresock_location)
        .arg("run")
        .arg("-config")
        .arg(wiresock_config_path)
        .arg("-log-level")
        .arg(log_level)
        .creation_flags(0x08000000) // CREATE_NO_WINDOW - stop a command window showing
        .stdout(Stdio::piped())
        .spawn()
        .expect("Unable to start WireSock process");

    // Update the WIRESOCK_STATE and emit the change
    update_state(&app_handle, |state| {
        state.wiresock_status = "RUNNING".to_string();
    });

    // Add process to global Job object
    // This ensures if TunnlTo process finishes, wiresock will also exit
    if let Some(tracker) = ChildProcessTracker::global() {
        tracker.add_process(child.as_raw_handle()).ok();
    }

    // Process the stdout data
    if let Some(stdout) = &mut child.stdout.take() {
        let reader = BufReader::new(stdout);

        for line in reader.lines() {
            let line_string = line.unwrap();

            if line_string.is_empty() {
                continue;
            }

            if cfg!(debug_assertions) {
                println!("wiresock_log: {}", line_string);
            }

            // Update the WIRESOCK_STATE and emit the change
            update_state(&app_handle, |state| {
                if line_string.contains("Tunnel has started") {
                    state.tunnel_status = "CONNECTED".to_string();
                }

                // Lock the mutex to safely access LOG_LIMIT
                let log_limit = LOG_LIMIT.lock().unwrap();

                // Check if the logs array has reached the maximum limit
                if state.logs.len() >= (*log_limit).try_into().unwrap() {
                    // Remove the oldest log
                    state.logs.remove(0);
                }

                // Append the log data to the state
                state.logs.push(line_string.clone());
            });
        }
    }

    // Handle the wiresock process stopping
// Handle the wiresock process stopping
match child.wait() {
    Ok(status) => {
        println!("wiresock process exited with: {}", status);

        // 检查是否成功（退出码为0）
        if status.success() {
            update_state(&app_handle, |state| {
                state.wiresock_status = "STOPPED".to_string();
                state.tunnel_status = "DISCONNECTED".to_string();
                state.logs.push("Tunnel disabled normally".into());
            });
            Ok(())
        } else {
            let error_msg = format!(
                "WireSock failed with exit code: {}",
                status.code().unwrap_or(-1)
            );
            update_state(&app_handle, |state| {
                state.wiresock_status = "ERROR".to_string();
                state.tunnel_status = "FAILED".to_string();
                state.logs.push(error_msg.clone().into());
            });
            Err(error_msg)
        }
    }
    Err(e) => {
        let error_msg = format!("Failed to wait for wiresock: {}", e);
        update_state(&app_handle, |state| {
            state.wiresock_status = "ERROR".to_string();
            state.tunnel_status = "FAILED".to_string();
            state.logs.push(error_msg.clone().into());
        });
        Err(error_msg)
    }
}
}
async fn disable_wiresock() -> Result<(), String> {
    println!("Attempting to kill the WireSock process");
    let status = Command::new("taskkill")
        .arg("/F")
        .arg("/IM")
        .arg("wiresock-client.exe")
        .arg("/T")
        .creation_flags(0x08000000) // CREATE_NO_WINDOW - stop a command window showing
        .status();

    match status {
        Ok(exit_status) => match exit_status.code() {
            Some(0) | Some(128) => {
                println!("WireSock successfully stopped or was not running.");
                Ok(())
            }
            Some(error_code) => {
                println!("Failed to stop WireSock, exit code: {}", error_code);
                Err(format!(
                    "Failed to stop WireSock, exit code: {}",
                    error_code
                ))
            }
            None => {
                println!("Failed to stop WireSock, no exit code available.");
                Err("Failed to stop WireSock, no exit code available.".to_string())
            }
        },
        Err(e) => {
            println!("Failed to execute taskkill command: {}", e);
            Err(format!("Failed to execute taskkill command: {}", e))
        }
    }
}
