import { createApp } from "vue";
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './styles/reset.scss'
// 如果您正在使用CDN引入，请删除下面一行。
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import baseBtn from './components/btn/btn.vue'
import copyBtn from './components/btn/copy.vue'
import router from './router'
import App from "./App.vue";
import Pinia from './store'

const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.component('baseBtn', baseBtn)
app.component('copyBtn', copyBtn)
app.use(router)
app.use(ElementPlus)
app.use(Pinia)
app.mount('#app')

