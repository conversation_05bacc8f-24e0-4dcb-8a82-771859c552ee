use clap::Parser;

#[cfg(target_os = "windows")]
pub const DEFAULT_LOG_DIR: &str = "/Logs/sdw-service";
#[cfg(not(target_os = "windows"))]
pub const DEFAULT_LOG_DIR: &str = "/var/log/sdw-service";

#[derive(Debug, Parse<PERSON>, Clone)]
#[clap(about = "Sdw VPN client interface management service")]
#[command(version)]
pub struct Config {
    /// Configures log level of sdw service logs
    #[arg(long, env = "SDW_LOG_LEVEL", default_value = "info")]
    pub log_level: String,

    /// Configures logging directory; it is meant for debugging only, so hide it.
    #[arg(long, env = "SDW_LOG_DIR", default_value = DEFAULT_LOG_DIR, hide = true)]
    pub log_dir: String,

    /// Defines how often (in seconds) interface statistics are sent to sdw client
    #[arg(long, short = 'p', env = "SDW_STATS_PERIOD", default_value = "10")]
    pub stats_period: u64,
}
